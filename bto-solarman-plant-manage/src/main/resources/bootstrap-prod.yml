spring:
  cloud:
    nacos:
      config:
        namespace: light-cloud
      discovery:
        namespace: light-cloud
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl
logging:
  config: classpath:log4j2-prod.xml
seata:
  tx-service-group: ${spring.application.name}-group
  service:
    grouplist:
      solarman: 8.134.12.233:8091
    vgroup-mapping:
      solarman-plant-group: solarman