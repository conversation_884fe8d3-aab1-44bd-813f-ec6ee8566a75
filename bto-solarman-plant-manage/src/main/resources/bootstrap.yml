server:
  port: 41200
spring:
  profiles:
    active: dev
  application:
    name: solarman-plant
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        file-extension: yaml
        refresh-enabled: false
        extension-configs:
          - data-id: solarman-datasource.yaml
            refresh: true
          - data-id: solarman-common.yaml
            refresh: true
      discovery:
        server-addr: 127.0.0.1:8848
        service: solarman-plant
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
async:
  executor:
    thread:
      core_pool_size: 100
      max_pool_size: 100
      queue_capacity: 99988
      name:
        prefix: async-importDB-