<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.RepairRecordsDao">

    <resultMap type="com.bto.commons.pojo.entity.RepairRecordsEntity" id="repairRecordsMap">
        <result property="id" column="id"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="deviceIdOld" column="device_id_old"/>
        <result property="deviceIdNew" column="device_id_new"/>
        <result property="deviceType" column="device_type"/>
        <result property="maintainDate" column="maintain_date"/>
        <result property="serviceman" column="serviceman"/>
        <result property="maintain" column="maintain"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remarks" column="remarks"/>
    </resultMap>
    <select id="page" resultType="com.bto.commons.pojo.vo.RepairRecordsVO">
        SELECT
        t1.*,
        bpb.plant_name,
        t2.manufacturer,
        t2.module
        FROM
        bto_repair_records t1
        LEFT JOIN bto_plant_base bpb ON bpb.plant_uid = t1.plant_uid
        LEFT JOIN bto_device t2 ON t2.device_id = t1.device_id_new

        <where>
            <if test="query.plantName != null and query.plantName != ''">
                AND bpb.plant_name LIKE CONCAT('%', #{query.plantName}, '%')
            </if>
            <if test="query.id != null and query.id != ''">
                t1.id = #{query.id}
            </if>
            <if test="query.plantUid != null and query.plantUid != ''">
                AND t1.plant_uid = #{query.plantUid}
            </if>
            <if test="query.deviceIdNew != null and query.deviceIdNew != ''">
                AND t1.device_id_new = #{query.deviceIdNew}
            </if>
            <if test="query.deviceIdOld != null and query.deviceIdOld != ''">
                AND t1.deviceIdOld = #{query.deviceIdOld}
            </if>
            <if test="query.deviceType != null and query.deviceType != ''">
                AND t1.device_type = #{query.deviceType}
            </if>
            <if test="query.serviceman != null and query.serviceman != ''">
                AND t1.serviceman = #{query.serviceman}
            </if>
            <if test="query.remarks != null and query.remarks != ''">
                AND t1.remarks = #{query.remarks}
            </if>
            <if test="query.maintain != null and query.maintain != ''">
                AND t1.maintain LIKE CONCAT('%', #{query.maintain}, '%')
            </if>
            <if test="query.maintainDate != null">
                AND t1.maintain_date = #{query.maintainDate}
            </if>
        </where>

    </select>

</mapper>