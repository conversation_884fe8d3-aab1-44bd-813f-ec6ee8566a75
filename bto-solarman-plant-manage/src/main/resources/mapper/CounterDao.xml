<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.plant.dao.CounterDao">

    <sql id="roundFields">
        round
            (apv / 100, 2)
            apv,
        round(bpv / 100, 2) bpv,
        round(cpv / 100, 2) cpv,
        round(aac / 100, 2) aac,
        round(bac / 100, 2) bac,
        round(cac / 100, 2) cac
    </sql>

    <select id="getPage" resultType="com.bto.commons.pojo.entity.CounterEntity">
        WITH t_rank AS (SELECT *,
        RANK() OVER ( PARTITION BY cabinet_id ORDER BY collect_time DESC ) AS `rank`
        FROM bto_counter)
        SELECT
        cabinet_id,
        collect_time,
        status,
        <include refid="roundFields"/>
        FROM t_rank
        <where>
            AND `rank` = 1
            <if test="query.cabinetId!=null and query.cabinetId.size()>0">
                and cabinet_id IN
                <foreach collection="query.cabinetId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY cabinet_id ASC
    </select>
    <select id="getDeviceIdsById" resultType="java.lang.String">
        SELECT device_id
        from bto_has_device
        <where>
            is_deleted = 0
            <if test="cabinetId !=null and cabinetId.size()>0">
                and device_pid IN
                <foreach collection="cabinetId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <resultMap id="fieldAMapResult" type="map">
        <result property="key" column="device_pid"/>
        <result property="value" column="device_id"/>
    </resultMap>

    <select id="getMap" resultMap="fieldAMapResult">
        SELECT device_id,device_pid
        from bto_has_device
        <where>
            is_deleted = 0
            <if test="cabinetId !=null and cabinetId.size()>0">
                and device_pid IN
                <foreach collection="cabinetId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        order by device_pid
    </select>

</mapper>