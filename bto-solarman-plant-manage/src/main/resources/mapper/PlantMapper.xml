<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.plant.dao.PlantMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>

    <!--获取某月电站发电量-->
    <select id="getPlantElectricityStatsByMonth" resultType="java.util.HashMap">
        select
        collect dataTime , (electricity/100) powerOrElectricity
        from bto_plant_day
        <where>
            <if test="startTime!='' and startTime!=null">
                and collect &gt;= #{startTime}
            </if>
            <if test=" endTime!='' and endTime!=null">
                and collect &lt;= #{endTime}
            </if>
            <if test="plantUid!='' and plantUid!=null">
                and plant_uid=#{plantUid}
            </if>
        </where>
        order by dataTime asc
    </select>
    <!--获取某年电站发电量-->
    <select id="getPlantElectricityStatsByYear" resultType="java.util.HashMap">
        select
        left(collect, 7) dataTime , sum(electricity) powerOrElectricity
        from bto_plant_day
        <where>
            <if test="startTime!='' and startTime!=null ">
                and collect &gt;= #{startTime}
            </if>
            <if test="endTime!='' and endTime!=null">
                and collect &lt;= #{endTime}
            </if>
            <if test="plantUid!='' and plantUid!= null">
                and plant_uid=#{plantUid}
            </if>
            group by dataTime
            order by dataTime
        </where>
    </select>

    <!--获取电站列表-->
    <resultMap id="PlantVOMap" type="com.bto.commons.pojo.dto.PlantVO">
        <id column="plant_uid" property="plantUid"/>
        <result column="plant_name" property="plantName"/>
        <result column="plant_capacity" property="plantCapacity"/>
        <result column="orientationEnum" property="orientation"/>
        <result column="plant_status" property="plantStatus"/>
        <result column="plant_type_id" property="plantTypeId"/>
        <result column="inverter_num" property="inverterNum"/>
        <result column="power_distributor" property="powerDistributor"/>
        <result column="country" property="country"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="town" property="town"/>
        <result column="address" property="address"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="power" property="power"/>
        <result column="today_electricity" property="todayElectricity"/>
        <result column="month_electricity" property="monthElectricity"/>
        <result column="year_electricity" property="yearElectricity"/>
        <result column="total_electricity" property="totalElectricity"/>
        <result column="receive_time" property="receiveTime"/>
        <result column="sale_price" property="salePrice"/>
        <result column="project_special" property="projectSpecial"/>
        <result column="meter_id" property="meterId"/>
        <result column="user_uid" property="userUid"/>
        <result column="create_time" property="createTime"/>
        <collection property="inverterSN" ofType="java.lang.String">
            <result column="inverterSN"/>
        </collection>
        <!-- <result column="user_name" property="userName"></result>
        <result column="user_phone" property="userPhone"></result> -->
    </resultMap>
    <select id="getPlants" resultMap="PlantVOMap">
        SELECT
        bpb.*,
        bpb.project_special projectName,
        bd.device_id inverterSN
        FROM v_user_plant bpb
        LEFT JOIN bto_device bd on bpb.plant_uid = bd.plant_uid
        <where>
            <trim suffixOverrides="and | or">
                <include refid="userInfo"/>
                AND bpb.is_deleted='0'
                AND bd.is_deleted = '0'
                AND bd.device_type = '1'
                <if test="plantVO.projectSpecial !='' and plantVO.projectSpecial !=null">
                    and bpb.project_special like concat('%',#{plantVO.projectSpecial},'%')
                </if>
                <if test="plantVO.plantName != '' and plantVO.plantName != null">
                    and bpb.plant_name like concat('%',#{plantVO.plantName},'%')
                </if>
                <if test="plantVO.country !='' and plantVO.country !=null">
                    and bpb.country = #{plantVO.country}
                </if>
                <if test="plantVO.province !='' and plantVO.province !=null">
                    and bpb.province = #{plantVO.province}
                </if>
                <if test="plantVO.city !='' and plantVO.city !=null">
                    and bpb.city = #{plantVO.city}
                </if>
                <if test="plantVO.area !='' and plantVO.area !=null">
                    and bpb.area = #{plantVO.area}
                </if>
                <if test="plantVO.town !='' and plantVO.town !=null">
                    and bpb.town = #{plantVO.town}
                </if>
                <if test="plantVO.address !='' and plantVO.address !=null">
                    and bpb.address like concat('%',#{plantVO.address},'%')
                </if>
                <if test="plantVO.plantTypeId !='' and plantVO.plantTypeId != null">
                    and bpb.plant_type_id = #{plantVO.plantTypeId}
                </if>
                <if test="plantVO.maxPlantCapacity !='' and plantVO.minPlantCapacity !=null">
                    and bpb.plant_capacity &lt;= #{plantVO.minPlantCapacity}
                </if>
                <if test="plantVO.minPlantCapacity !='' and plantVO.minPlantCapacity !=null">
                    and bpb.plant_capacity &gt;= #{plantVO.maxPlantCapacity}
                </if>
                <if test="plantVO.multiPlantStatus !=null and plantVO.multiPlantStatus.size()>0">
                    and bpb.plant_status in
                    <foreach collection="plantVO.multiPlantStatus" item="plantStatus" open="(" close=")" index="index"
                             separator=",">
                        #{plantStatus}
                    </foreach>
                </if>
                <if test="plantVO.createTimeStart !='' and plantVO.createTimeStart !=null">
                    and bpb.create_time &gt;= #{plantVO.createTimeStart}
                </if>
                <if test="plantVO.createTimeEnd !='' and plantVO.createTimeEnd !=null">
                    and bpb.create_time &lt;= #{plantVO.createTimeEnd}
                </if>
                <if test="plantVO.powerDistributor !='' and plantVO.powerDistributor !=null">
                    and bpb.power_distributor =#{plantVO.powerDistributor}
                </if>
            </trim>
        </where>
        GROUP BY bd.plant_uid,bd.device_id
        ORDER BY bpb.${columnName} ${sortType}
    </select>

    <update id="updatePlantInfo" parameterType="com.bto.commons.pojo.dto.PlantUpdateDTO" flushCache="true">
        update bto_plant_base
        <set>
            <trim suffixOverrides=",">
                <if test="plantUpdateDTO.plantName!=null and plantUpdateDTO.plantName!=''">
                    plant_name=#{plantUpdateDTO.plantName} ,
                </if>
                <if test="plantUpdateDTO.plantCapacity!=null and plantUpdateDTO.plantCapacity!=''">
                    plant_capacity=#{plantUpdateDTO.plantCapacity,javaType=string,jdbcType=INTEGER}*1000 ,
                </if>
                <if test="plantUpdateDTO.meterId!=null and plantUpdateDTO.meterId!=''">
                    meter_id=#{plantUpdateDTO.meterId,jdbcType=INTEGER,javaType=String} ,
                </if>
                <if test="plantUpdateDTO.longitude!=null and plantUpdateDTO.longitude!=''">
                    longitude=#{plantUpdateDTO.longitude} ,
                </if>
                <if test="plantUpdateDTO.latitude!=null and plantUpdateDTO.latitude!=''">
                    latitude=#{plantUpdateDTO.latitude} ,
                </if>
                <if test="plantUpdateDTO.area!=null and plantUpdateDTO.area!=''">
                    area=#{plantUpdateDTO.area} ,
                </if>
                <if test="plantUpdateDTO.city!=null and plantUpdateDTO.city!=''">
                    city=#{plantUpdateDTO.city} ,
                </if>
                <if test="plantUpdateDTO.province!=null and plantUpdateDTO.province!=''">
                    province=#{plantUpdateDTO.province} ,
                </if>
                <if test="plantUpdateDTO.address!=null and plantUpdateDTO.address!=''">
                    address=#{plantUpdateDTO.address} ,
                </if>
            </trim>
        </set>
        where
        plant_uid=#{plantUpdateDTO.plantUid}
    </update>

    <select id="selectPlantInfo" resultType="com.bto.commons.pojo.vo.PlantVO">
        SELECT plant_uid         plantUid,
               plant_name        plantName,
               plant_capacity    plantCapacity,
               orientation,
               plant_status      plantStatus,
               plant_type_id     plantTypeId,
               inverter_num      inverterNum,
               power_distributor powerDistributor,
               country,
               province,
               city,
               area,
               town,
               address,
               longitude,
               latitude,
               power,
               today_electricity todayElectricity,
               month_electricity monthElectricity,
               year_electricity  yearElectricity,
               total_electricity totalElectricity,
               receive_time      receiveTime,
               sale_price        salePrice,
               project_special   projectId,
               meter_id          meterId,
               user_uid          userUid,
               user_name         userName,
               user_phone        userPhone,
               create_time       createTime
        FROM v_user_plant
        WHERE plant_uid = #{plantUid}
    </select>
    <select id="getPlantCount" resultType="java.lang.Integer">
        SELECT
        count(bpb.plant_uid)
        FROM bto_plant_base bpb
        <where>
            <trim suffixOverrides="and | or">
                <include refid="userInfo"/>
                AND bpb.is_deleted='0'
                <if test="plantVO.projectSpecial !='' and plantVO.projectSpecial !=null">
                    and bpb.project_special like concat(#{plantVO.projectSpecial},'%')
                </if>
                <if test="plantVO.plantName != '' and plantVO.plantName != null">
                    and bpb.plant_name like concat('%',#{plantVO.plantName},'%')
                </if>
                <if test="plantVO.country !='' and plantVO.country !=null">
                    and bpb.country = #{plantVO.country}
                </if>
                <if test="plantVO.province !='' and plantVO.province !=null">
                    and bpb.province = #{plantVO.province}
                </if>
                <if test="plantVO.city !='' and plantVO.city !=null">
                    and bpb.city = #{plantVO.city}
                </if>
                <if test="plantVO.area !='' and plantVO.area !=null">
                    and bpb.area = #{plantVO.area}
                </if>
                <if test="plantVO.town !='' and plantVO.town !=null">
                    and bpb.town = #{plantVO.town}
                </if>
                <if test="plantVO.address !='' and plantVO.address !=null">
                    and bpb.address like concat('%',#{plantVO.address},'%')
                </if>
                <if test="plantVO.plantTypeId !='' and plantVO.plantTypeId != null">
                    and bpb.plant_type_id = #{plantVO.plantTypeId}
                </if>
                <if test="plantVO.maxPlantCapacity !='' and plantVO.minPlantCapacity !=null">
                    and bpb.plant_capacity &lt;= #{plantVO.minPlantCapacity}
                </if>
                <if test="plantVO.minPlantCapacity !='' and plantVO.minPlantCapacity !=null">
                    and bpb.plant_capacity &gt;= #{plantVO.maxPlantCapacity}
                </if>
                <if test="plantVO.multiPlantStatus !=null and plantVO.multiPlantStatus.size()>0">
                    and bpb.plant_status in
                    <foreach collection="plantVO.multiPlantStatus" item="plantStatus" open="(" close=")" index="index"
                             separator=",">
                        #{plantStatus}
                    </foreach>
                </if>
                <if test="plantVO.createTimeStart !='' and plantVO.createTimeStart !=null">
                    and bpb.create_time &gt;= #{plantVO.createTimeStart}
                </if>
                <if test="plantVO.createTimeEnd !='' and plantVO.createTimeEnd !=null">
                    and bpb.create_time &lt;= #{plantVO.createTimeEnd}
                </if>
                <if test="plantVO.powerDistributor !='' and plantVO.powerDistributor !=null">
                    and bpb.power_distributor =#{plantVO.powerDistributor}
                </if>
            </trim>
        </where>
    </select>
    <select id="getPlantList" resultType="com.bto.commons.pojo.vo.PlantInfoVO">
        SELECT
        v1.plant_uid plantUid,
        v1.plant_name plantName,
        v1.plant_status plantStatus,
        v1.project_company company,
        v1.plant_type_id plantType,
        v1.plant_capacity plantCapacity,
        v1.power_distributor powerDistributor,
        v1.project_special projectId,
        LEFT(v1.project_special,1) projectName,
        v1.project_special projectCompany,
        v1.meter_id meterId,
        v1.order_id orderId,
        v1.contract_id contractId,
        v1.user_name userName,
        v1.user_phone userPhone,
        v2.inverter_sn inverterSn,
        v1.receive_time receiveTime,
        v1.sale_price salePrice,
        v1.country,
        v1.province,
        v1.city,
        v1.area,
        v1.town,
        v1.address,
        v1.today_electricity todayElectricity,
        v1.month_electricity monthElectricity,
        v1.year_electricity yearElectricity,
        v1.total_electricity totalElectricity,
        v1.power,
        v1.inverter_num inverterNum,
        v1.orientation,
        v1.latitude,
        v1.longitude,
        v1.remarks     remarks,
        v1.create_time createTime,
        v1.create_time warrantyTime,
        v1.update_time updateTime
        FROM v_user_plant v1
        LEFT JOIN v_inverter_list v2 ON v1.plant_uid = v2.plant_uid
        <where>
            <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
                and v1.plant_uid in
                <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
                and v1.project_special in
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
            </if>
            <!--查询条件-->
            <if test="query.plantUid !='' and query.plantUid !=null">
                and v1.plant_uid like concat(#{query.plantUid},'%')
            </if>
            <if test="query.projectId !='' and query.projectId !=null">
                and v1.project_special like concat(#{query.projectId},'%')
            </if>
            <if test="query.plantName != '' and query.plantName != null">
                and v1.plant_name like concat('%',#{query.plantName},'%')
            </if>
            <if test="query.province !='' and query.province !=null">
                and v1.province like concat('%',#{query.province},'%')
            </if>
            <if test="query.city !='' and query.city !=null">
                and v1.city like concat('%',#{query.city},'%')
            </if>
            <if test="query.area !='' and query.area !=null">
                and v1.area like concat('%',#{query.area},'%')
            </if>
            <if test="query.town !='' and query.town !=null">
                and v1.town like concat('%',#{query.town},'%')
            </if>
            <if test="query.address !='' and query.address !=null">
                and v1.address like concat('%',#{query.address},'%')
            </if>
            <if test="query.plantType !='' and query.plantType != null">
                and v1.plant_type_id = #{query.plantType}
            </if>
            <if test="query.minPlantCapacity !='' and query.minPlantCapacity !=null">
                and v1.plant_capacity &gt; #{query.minPlantCapacity}
            </if>
            <if test="query.maxPlantCapacity !='' and query.maxPlantCapacity !=null">
                and v1.plant_capacity &lt; #{query.maxPlantCapacity}
            </if>
            <if test="query.multiPlantStatus !=null and query.multiPlantStatus.size()>0">
                and v1.plant_status in
                <foreach collection="query.multiPlantStatus" item="status" open="(" close=")" index="index"
                         separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="query.startCreateTime !='' and query.startCreateTime !=null">
                and left(v1.create_time,10) &gt;= #{query.startCreateTime}
            </if>
            <if test="query.endCreateTime !='' and query.endCreateTime !=null">
                and left(v1.create_time,10) &lt;= #{query.endCreateTime}
            </if>
            <if test="query.powerDistributor !='' and query.powerDistributor !=null">
                and v1.power_distributor =#{query.powerDistributor}
            </if>
        </where>
        ORDER BY ${query.order}
        <choose>
            <when test="query.isAsc">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>
    <select id="selectAll" resultType="com.bto.commons.pojo.vo.PlantIdWithNameVO">
        WITH w_pid AS(
        WITH RECURSIVE project_hierarchy AS (
        SELECT id, pid
        FROM bto_project_category
        WHERE is_deleted = 0 AND pid = #{projectId}
        UNION ALL
        SELECT c.id, c.pid
        FROM bto_project_category c
        JOIN project_hierarchy p ON c.pid = p.id
        WHERE c.is_deleted = 0
        )
        SELECT id FROM project_hierarchy
        UNION
        SELECT id FROM bto_project_category WHERE is_deleted = 0 AND id = #{projectId}
        )
        select plant_uid, plant_name,plant_capacity
        FROM v_user_plant bpb
        <where>
            bpb.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="projectId !='' and projectId !=null">
                and bpb.project_special IN (SELECT id FROM w_pid)
            </if>
            <if test="plantName !='' and plantName !=null">
                and bpb.plant_name like concat('%',#{plantName},'%')
            </if>
        </where>
        order by create_time desc
    </select>
    <select id="getElectricityByCity" resultType="com.bto.commons.pojo.vo.BatteryDivinerVO">
        WITH plant_day AS (SELECT plant_uid, collect, electricity
                           FROM bto_plant_day
                           WHERE collect > (CURDATE() - INTERVAL 7 DAY))
        SELECT SUM(pd.electricity) / 100 electricity,
               pd.collect                collectDate,
               bpb.city
        FROM plant_day pd
                 LEFT JOIN bto_plant_base bpb ON pd.plant_uid = bpb.plant_uid
        WHERE bpb.is_deleted = 0
          AND bpb.city = #{city}
        GROUP BY pd.collect
    </select>

    <!--    List<String> getPlantCities (@Param("userInfo") RequireParamsDTO userInfo,@Param("query") PlantEvaluationQuery query);-->
    <select id="getPlantCities" resultType="string">
        SELECT DISTINCT city
        FROM bto_plant_info bpb
        <where>
            bpb.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="query.projectId !='' and query.projectId !=null">
                AND bpb.project_special LIKE CONCAT(#{query.projectId},'%')
            </if>
        </where>
    </select>
    <!--    BigDecimal getElectricity(@Param("plantUidList") List<String> plantUidList,@Param("startTime") String startTime, @Param("endTime") String endTime,@Param("userInfo") RequireParamsDTO userInfo);-->
    <select id="getElectricityByDate" resultType="java.util.Map">
        SELECT
        DATE_FORMAT(collect, '%Y-%m-%d') AS collectDate,
        ROUND(SUM(CAST(electricity AS DECIMAL(10,2))) / 100, 2) AS totalElectricity
        FROM
        bto_plant_day
        <where>
            <if test="plantUidList != null and plantUidList.size() > 0">
                AND plant_uid IN
                <foreach collection="plantUidList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND collect BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        collect
        ORDER BY
        collect DESC
    </select>
    <!--    List<Map<Date, String>> getElectricityByMonth(@Param("plantUidList") List<String> plantUidList, @Param("startTime") String startTime, @Param("endTime") String endTime);-->
    <select id="getElectricityByMonth" resultType="java.util.LinkedHashMap">
        SELECT
        DATE_FORMAT(collect, '%Y-%m') AS collectDate,
        ROUND(SUM(CAST(electricity AS DECIMAL(10,2))) / 100, 2) AS totalElectricity
        FROM
        bto_plant_day
        <where>
            <if test="plantUidList != null and plantUidList.size() > 0">
                AND plant_uid IN
                <foreach collection="plantUidList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND collect BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        collectDate
        ORDER BY
        collectDate DESC;
    </select>
    <select id="getPlantCapacityByProjectId" resultType="java.lang.Integer">
        select sum(plant_capacity) totalCapacity
        from bto_plant_info bpb
        <where>
            bpb.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="query.projectId !='' and query.projectId !=null">
                and bpb.project_special like concat(#{query.projectId},'%')
            </if>
        </where>
    </select>

    <!--    PlantVO getPlantIdWithCity(@Param("userInfo") RequireParamsDTO userInfo);-->
    <select id="getPlantIdWithCity" resultType="com.bto.commons.pojo.dto.PlantVO">
        WITH w_pid AS(
        WITH RECURSIVE project_hierarchy AS (
        SELECT id, pid
        FROM bto_project_category
        WHERE is_deleted = 0 AND pid = #{projectId}
        UNION ALL
        SELECT c.id, c.pid
        FROM bto_project_category c
        JOIN project_hierarchy p ON c.pid = p.id
        WHERE c.is_deleted = 0
        )
        SELECT id FROM project_hierarchy
        UNION
        SELECT id FROM bto_project_category WHERE is_deleted = 0 AND id = #{projectId}
        )
        SELECT plant_uid, city
        FROM v_user_plant bpb
        <where>
            bpb.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="projectId !='' and projectId !=null">
                AND bpb.project_special IN ( SELECT id FROM w_pid)
            </if>
        </where>
    </select>

    <select id="getInStationPlantPage" resultType="com.bto.commons.pojo.vo.PlantBaseInfoVO">
        SELECT
        plant_uid plantUid,
        plant_name plantName
        FROM bto_plant_info
        <where>
            <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
                and plant_uid in
                <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
                and project_special in
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
            </if>
            <!--查询条件-->
            <if test="query.plantUid !='' and query.plantUid !=null">
                and plant_uid like concat(#{query.plantUid},'%')
            </if>
            <if test="query.plantName != '' and query.plantName != null">
                and plant_name like concat('%',#{query.plantName},'%')
            </if>

            <if test="query.stationPlantList !=null and query.stationPlantList.size()>0">
                and city in
                <foreach collection="query.stationPlantList" item="city" open="(" close=")" index="index" separator=",">
                    #{city}
                </foreach>
            </if>
        </where>
        ORDER BY ${query.order}
        <choose>
            <when test="query.isAsc">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>
    </select>

    <select id="getPowerPlantInfoByLocation" resultType="com.bto.commons.pojo.vo.PowerPlantInfoVO">
        SELECT
        t2.plant_uid,
        t2.collect,
        t2.electricity,
        bpb.plant_name,
        t1.bank_angle,
        bpb.orientation,
        t1.azimuth,
        bpb.plant_capacity,
        bpb.province,
        bpb.city,
        bpb.area,
        bpb.town
        FROM
        bto_plant_day t2
        JOIN bto_plant_info t1 ON t1.plant_uid = t2.plant_uid
        JOIN bto_plant_base bpb ON bpb.plant_uid = t2.plant_uid
        <where>
            bpb.is_deleted = '0'
            <include refid="userInfo"/>
            <if test="query.plantName!=null and query.plantName !=null">
                AND bpb.plant_name = #{query.plantName}
            </if>
            <if test="query.startCreateTime!=null and query.endCreateTime !=null">
                AND t2.collect between (#{query.startCreateTime}) AND (#{query.endCreateTime})
            </if>
            <if test="query.province != null and query.province.size() > 0">
                AND bpb.province IN
                <foreach collection="query.province" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.city != null and query.city.size() > 0">
                AND bpb.city IN
                <foreach collection="query.city" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by t2.collect DESC,t2.electricity DESC
    </select>

    <!--    IPage<WorkEfficiencyVO> getPlantElectricityRank(@Param("query") PowerPlantInfoQueryDTO query, @Param("userInfo") RequireParamsDTO userInfo, @Param("page") Page<PowerPlantInfoVO> page);-->
    <select id="getPlantElectricityRank" resultType="com.bto.commons.pojo.vo.WorkEfficiencyVO">
        SELECT v1.plant_uid,
        bpb.plant_name,
        v1.inverter_sn inverterId,
        v1.today_electricity
        FROM v_inverter_electricity v1
        INNER JOIN bto_plant_base bpb ON v1.plant_uid = bpb.plant_uid
        <where>
            v1.deleted = 0
            <include refid="userInfo"/>
            <if test="query.plantName != null and query.plantName.size() > 0">
                AND bpb.plant_name IN
                <foreach collection="query.plantName" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.province != null and query.province.size() > 0">
                AND bpb.province IN
                <foreach collection="query.province" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.city != null and query.city.size() > 0">
                AND bpb.city IN
                <foreach collection="query.city" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY today_electricity DESC
    </select>
    <select id="getPlantNameListInWeatherStation" resultType="java.lang.String">
        SELECT distinct station_name
        from bto_weather_station
        where device_address = #{deviceAddress}
    </select>
    <select id="getCitiesByPid" resultType="java.lang.String">
        WITH w_pid AS(
        WITH RECURSIVE project_hierarchy AS (
        SELECT id, pid
        FROM bto_project_category
        WHERE is_deleted = 0 AND pid = #{projectId}
        UNION ALL
        SELECT c.id, c.pid
        FROM bto_project_category c
        JOIN project_hierarchy p ON c.pid = p.id
        WHERE c.is_deleted = 0
        )
        SELECT id FROM project_hierarchy
        UNION
        SELECT id FROM bto_project_category WHERE is_deleted = 0 AND id = #{projectId}
        )
        SELECT distinct city
        from bto_plant_info bpb
        <where>
            <include refid="userInfo"/>
            AND bpb.is_deleted='0'
            <if test="projectId != null and projectId !=''">
                AND bpb.project_special IN ( SELECT id FROM w_pid)
            </if>
        </where>
    </select>

    <select id="getProjectListByCities" resultType="com.bto.commons.pojo.vo.ProjectInfoVO">
        SELECT bpc.id,
        bpc.pid,
        bpc.name  projectName
        FROM bto_project_category bpc
        WHERE bpc.id IN (
        SELECT DISTINCT bpb.project_special
        FROM bto_plant_info bpb
        <where>
            bpb.is_deleted='0'
            <if test="cities != null and cities.size() > 0">
                AND bpb.city IN
                <foreach collection="cities" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        )
    </select>
    <select id="getPlantListResult" resultType="com.bto.commons.pojo.vo.PlantListCountResultVO">
        SELECT
        SUM(v1.plant_capacity) plantCapacity,
        SUM(v1.today_electricity) todayElectricity,
        SUM(v1.month_electricity) monthElectricity,
        SUM(v1.year_electricity) yearElectricity,
        SUM(v1.total_electricity) totalElectricity
        FROM v_user_plant v1
        <where>
            <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
                and v1.plant_uid in
                <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
                and v1.project_special in
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
            </if>
            <!--查询条件-->
            <if test="query.plantUid !='' and query.plantUid !=null">
                and v1.plant_uid like concat(#{query.plantUid},'%')
            </if>
            <if test="query.projectId !='' and query.projectId !=null">
                and v1.project_special like concat(#{query.projectId},'%')
            </if>
            <if test="query.plantName != '' and query.plantName != null">
                and v1.plant_name like concat('%',#{query.plantName},'%')
            </if>
            <if test="query.province !='' and query.province !=null">
                and v1.province like concat('%',#{query.province},'%')
            </if>
            <if test="query.city !='' and query.city !=null">
                and v1.city like concat('%',#{query.city},'%')
            </if>
            <if test="query.area !='' and query.area !=null">
                and v1.area like concat('%',#{query.area},'%')
            </if>
            <if test="query.town !='' and query.town !=null">
                and v1.town like concat('%',#{query.town},'%')
            </if>
            <if test="query.address !='' and query.address !=null">
                and v1.address like concat('%',#{query.address},'%')
            </if>
            <if test="query.plantType !='' and query.plantType != null">
                and v1.plant_type_id = #{query.plantType}
            </if>
            <if test="query.minPlantCapacity !='' and query.minPlantCapacity !=null">
                and v1.plant_capacity &gt; #{query.minPlantCapacity}
            </if>
            <if test="query.maxPlantCapacity !='' and query.maxPlantCapacity !=null">
                and v1.plant_capacity &lt; #{query.maxPlantCapacity}
            </if>
            <if test="query.multiPlantStatus !=null and query.multiPlantStatus.size()>0">
                and v1.plant_status in
                <foreach collection="query.multiPlantStatus" item="status" open="(" close=")" index="index"
                         separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="query.startCreateTime !='' and query.startCreateTime !=null">
                and left(v1.create_time,10) &gt;= #{query.startCreateTime}
            </if>
            <if test="query.endCreateTime !='' and query.endCreateTime !=null">
                and left(v1.create_time,10) &lt;= #{query.endCreateTime}
            </if>
            <if test="query.powerDistributor !='' and query.powerDistributor !=null">
                and v1.power_distributor =#{query.powerDistributor}
            </if>
        </where>
    </select>
    <select id="getAmmeterByPlantUid" resultType="java.lang.String">
        SELECT
            device_id,
            device_type
        FROM
            bto_device
        WHERE
            device_type = 32
          AND meter_type IN (0,1)
          AND plant_uid = #{plantUid}
    </select>
    <select id="generateUid" resultType="java.lang.String">
        SELECT fun_produce_uid(#{type})
    </select>
    <select id="getStoredEnergyInfo" resultType="com.bto.commons.pojo.entity.BtoBattery">
        SELECT
            `id`,
            `inverter_sn`,
            `init_time`,
            `parallel_enable`,
            `parallel_master`,
            `module_sn`,
            `invtime`,
            `metera_status`,
            `metera_volt1`,
            `metera_volt2`,
            `metera_volt3`,
            `metera_curr1`,
            `metera_curr2`,
            `metera_curr3`,
            `metera_power_watt1`,
            `metera_power_watt2`,
            `metera_power_watt3`,
            `metera_power_va1`,
            `metera_power_va2`,
            `metera_power_va3`,
            `metera_freq1`,
            `metera_freq2`,
            `metera_freq3`,
            `bat_tempc`,
            `bat_energy_percent`,
            `bat_power`,
            `total_pv_power`,
            `total_load_powerwatt`,
            `total_grid_powerwatt`,
            `backup_total_load_powerwatt`,
            `rgrid_powerwatt`,
            `rinv_volt`,
            `rinv_curr`,
            `rinv_freq`,
            `rinv_powerwatt`,
            `sgrid_powerwatt`,
            `sinv_volt`,
            `sinv_curr`,
            `sinv_freq`,
            `sinv_powerwatt`,
            `tgrid_powerwatt`,
            `tinv_volt`,
            `tinv_curr`,
            `tinv_freq`,
            `tinv_powerwatt`,
            parall_total_pvmeter_energy / 100 AS parallTotalPvmeterEnergy,
            total_feedin_energy / 100 AS totalFeedinEnergy,
            total_load_energy / 100 AS totalLoadEnergy,
            total_batdis_energy / 100 AS totalBatdisEnergy,
            total_batchg_energy / 100 AS totalBatchgEnergy,
            `grid_direction`,
            today_load_energy / 100 AS todayLoadEnergy,
            today_sell_energy / 100 AS todaySellEnergy,
            today_feedin_energy / 100 AS todayFeedinEnergy,
            today_batdis_energy / 100 AS todayBatdisEnergy,
            today_batchg_energy / 100 AS todayBatchgEnergy,
            `battery_direction`,
            rout_volt / 100 AS routVolt,
            rout_curr / 100 AS routCurr,
            rout_freq / 100 AS routFreq,
            rout_powerwatt,
            sout_volt / 100 AS soutVolt,
            sout_curr / 100 AS soutCurr,
            sout_freq / 100 AS soutFreq,
            sout_powerwatt,
            tout_volt / 100 AS toutVolt,
            tout_curr / 100 AS toutCurr,
            tout_freq / 100 AS toutFreq,
            tout_powerwatt,
            `sysgrid_powerwatt`,
            `systotal_loadwatt`,
            `total_battery_power`,
            `tout_powerwatt`,
            `total_sell_energy`,
            `tgrid_powerpf`,
            `total_hour`,
            `iso1`,
            `iso2`,
            `iso3`,
            `iso4`,
            `pf`,
            `q_power`,
            `mpv_mode`,
            `is_online`,
            `mfault_msg`,
            `hfault_msg`,
            `fault_msglist`,
            `inv_tempc`,
            bat_capacity / 100 AS batCapacity,
            `aver_bat1_vol`,
            `aver_bat1_cur`,
            `aver_bat1_soc`,
            `aver_bat2_vol`,
            `aver_bat2_cur`,
            `aver_bat2_soc`,
            `aver_bat3_vol`,
            `aver_bat3_cur`,
            `aver_bat3_soc`,
            `link_signal`,
            sink_tempc / 100 AS sinkTempc,
            amb_tempc / 100 AS ambTempc,
            bms_user_energy / 100 AS bmsUserEnergy,
            bms_total_power / 100 AS bmsTotalPower,
            bms_total_energy / 100 AS bmsTotalEnergy,
            `battery_group_datalist`,
            `grid_powerpf`,
            `sgrid_powerpf`,
            `source`,
            `update_time`
        FROM ${tableName}
        <where>
            `inverter_sn` = #{inverterSn}
        </where>
        ORDER BY `update_time` DESC
            LIMIT 1
    </select>

    <select id="getStoredEnergyBaseInfo" resultType="com.bto.commons.pojo.vo.BatteryBaseVO">
        SELECT
        t1.`inverter_sn`,
        t1.`init_time`,
        `module_sn`,
        `invtime`,
        `metera_status`,
        t1.total_feedin_energy / 100 AS totalFeedinEnergy,
        t1.total_load_energy / 100 AS totalLoadEnergy,
        t1.total_batdis_energy / 100 AS totalBatdisEnergy,
        t1.total_batchg_energy / 100 AS totalBatchgEnergy,
        `sysgrid_powerwatt`,
        `systotal_loadwatt`,
        `total_sell_energy`,
        t1.`bat_power`,
        `mpv_mode`,
        `is_online`,
        t3.bat_capacity AS batCapacity,
        t3.`status` AS status,
        CONCAT(FLOOR(t3.work_time / 60), '小时', MOD(t3.work_time, 60), '分钟') AS workTimeStr,
        `link_signal`,
        sink_tempc / 100 AS sinkTempc,
        amb_tempc / 100 AS ambTempc,
        `battery_group_datalist`,
        t2.`load_energy` / 100 AS loadEnergy,
        t2.`sell_energy` / 100 AS sellEnergy,
        t2.`feedin_energy` / 100 AS feedinEnergy,
        t2.`batchg_energy` / 100 AS batchgEnergy,
        t2.`batdis_energy` / 100 AS batdisEnergy,
        t2.`self_energy` / 100 AS selfEnergy,
        t2.`autarky_energy` / 100 AS autarkyEnergy
        FROM
        ${tableName} t1
        LEFT JOIN bto_battery_day t2 ON t1.inverter_sn = t2.inverter_sn
        LEFT JOIN bto_battery_latest t3 ON t1.inverter_sn = t3.inverter_sn
        <where>
            t1.`inverter_sn` = #{inverterSn}
            AND t2.collect = #{collect}
        </where>
        ORDER BY t1.`update_time` DESC
        LIMIT 1
    </select>
    <select id="getMeterData" resultType="com.bto.commons.pojo.entity.MeterData">
        WITH w_m AS (
            SELECT
                plant_uid,
                voltmeter_id meterNumber,
                ROUND(MAX(total_impep_display) OVER (PARTITION BY voltmeter_id) / 100, 2) AS thisMonthReading,
                ROUND(MAX(total_expep_display) OVER (PARTITION BY voltmeter_id) / 100, 2) AS reverseThisMonth,
                ROUND(MIN(total_impep_display) OVER (PARTITION BY voltmeter_id) / 100, 2) AS lastMonthReading,
                ROUND(MIN(total_expep_display) OVER (PARTITION BY voltmeter_id) / 100, 2) AS reverseLastMonth
            FROM
                bto_infrared_meter
            WHERE
                plant_uid = #{query.plantUid}
              AND init_time REGEXP #{query.regexpDate}
            ),
            w_diff AS ( SELECT plant_uid, meterNumber, thisMonthReading, lastMonthReading, reverseThisMonth, reverseLastMonth FROM w_m GROUP BY meterNumber ) SELECT
              CASE

                  WHEN
                      t1.meter_type = 1 THEN
                      '光伏电表'
                  WHEN t1.meter_type = 0 THEN
                      '供电电表' ELSE '未知类型'
                  END AS meterType,
              t1.current_transformer multiplier,
              t1.install_address installAddress,
              bpb.plant_name,
              w1.*
          FROM
              bto_device t1
                  LEFT JOIN w_diff w1 ON t1.device_id = w1.meterNumber
                  LEFT JOIN bto_plant_base bpb ON bpb.plant_uid = w1.plant_uid
          WHERE
              t1.plant_uid = #{query.plantUid}
            AND t1.device_type = 32
            AND t1.is_deleted = 0
    </select>
    <select id="getElectricityMeterBill" resultType="com.bto.commons.pojo.vo.ElectricityBillingVO">
        WITH pv AS (
        SELECT
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN sharp_peak_impep END ) AS sharp_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN sharp_peak_impep END ) AS sharp_start,
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN peak_impep END ) AS peak_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN peak_impep END ) AS peak_start,
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN normal_impep END ) AS normal_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN normal_impep END ) AS normal_start,
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN valley_impep END ) AS valley_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN valley_impep END ) AS valley_start
        FROM
        bto_infrared_meter
        WHERE
        plant_uid = #{query.plantUid}
        AND voltmeter_id = #{query.photovoltaicMeterId}
        ),
        grid AS (
        SELECT
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN sharp_peak_expep END ) AS sharp_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN sharp_peak_expep END ) AS sharp_start,
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN peak_expep END ) AS peak_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN peak_expep END ) AS peak_start,
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN normal_expep END ) AS normal_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN normal_expep END ) AS normal_start,
        MAX( CASE WHEN DATE( create_time ) = #{query.endTime} THEN valley_expep END ) AS valley_end,
        MIN( CASE WHEN DATE( create_time ) = #{query.startTime} THEN valley_expep END ) AS valley_start
        FROM
        bto_infrared_meter
        WHERE
        plant_uid = #{query.plantUid}
        AND voltmeter_id = #{query.powerMeterId}
        ),
        usage_kwh AS (
        SELECT
        ROUND((( pv.sharp_end - pv.sharp_start ) - ( grid.sharp_end - grid.sharp_start )) / 100.0, 2 ) AS sharp,
        ROUND((( pv.peak_end - pv.peak_start ) - ( grid.peak_end - grid.peak_start )) / 100.0, 2 ) AS peak,
        ROUND((( pv.normal_end - pv.normal_start ) - ( grid.normal_end - grid.normal_start )) / 100.0, 2 ) AS normal,
        ROUND((( pv.valley_end - pv.valley_start ) - ( grid.valley_end - grid.valley_start )) / 100.0, 2 ) AS valley
        FROM
        pv,
        grid
        ),
        price AS ( SELECT * FROM bto_plant_price WHERE plant_uid = #{query.plantUid} AND start_time &lt;= #{query.startTime} AND ( end_time IS NULL OR end_time &gt;= #{query.endTime} ) ),
        metering_rows AS (
        SELECT
        '尖' AS time_period,
        usage_kwh.sharp AS consumption_kwh,
        price.price_sharp AS unit_price,
        price.discount
        FROM
        usage_kwh,
        price UNION ALL
        SELECT
        '峰',
        usage_kwh.peak,
        price.price_peak,
        price.discount
        FROM
        usage_kwh,
        price UNION ALL
        SELECT
        '平',
        usage_kwh.normal,
        price.price_normal,
        price.discount
        FROM
        usage_kwh,
        price UNION ALL
        SELECT
        '谷',
        usage_kwh.valley,
        price.price_valley,
        price.discount
        FROM
        usage_kwh,
        price
        ) SELECT
        time_period,
        consumption_kwh,
        unit_price AS unit_price_cny_per_kwh,
        ROUND( consumption_kwh * unit_price, 2 ) AS amount_cny,
        ROUND( consumption_kwh * unit_price * discount, 2 ) AS discounted_amount_cny
        FROM
        metering_rows UNION ALL
        SELECT
        '合计',
        ROUND( SUM( consumption_kwh ), 2 ),
        NULL,
        ROUND( SUM( consumption_kwh * unit_price ), 2 ),
        ROUND( SUM( consumption_kwh * unit_price * discount ), 2 )
        FROM
        metering_rows;
    </select>
    <select id="hasStructureTemp" resultType="com.bto.commons.pojo.entity.Device">
        SELECT
            device_id,
            device_type
        FROM
            bto_device
        WHERE
            is_deleted = 0
          AND device_type IN ( 41, 42 )
          AND plant_uid = #{plantUid}
    </select>
    <select id="getPowerSummary" resultType="com.bto.commons.pojo.vo.WorkPowerDTO">
        SELECT
            pv_power,
            bat_power,
            load_power,
            grid_power,
            `status`,
            init_time
        FROM
            bto_work_power
        where inverter_sn = #{inverterSn}
        order by init_time desc
        limit 1
    </select>
</mapper>