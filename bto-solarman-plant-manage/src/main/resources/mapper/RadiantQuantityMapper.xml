<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.plant.dao.RadiantQuantityDao">

    <select id="getRadiateByThirtyDays" resultType="java.util.Map">
        SELECT
        DATE_FORMAT(collect_date, '%Y-%m-%d') AS collectDate,
        SUM(radiant_quantity) AS totalRadiant
        FROM
        bto_radiant_quantity
        <where>
            <if test="stationUidList != null and stationUidList.size() > 0">
                AND station_uid IN
                <foreach collection="stationUidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        collect_date
        ORDER BY
        collect_date DESC
        LIMIT 30
    </select>

    <!--    List<Map<Date, String>> getRadiationByDateMonth(RadiantQuantityQuery radiantQuantityQuery);-->

    <!--    <select id="getRadiationByDateMonth" resultType="java.util.Map">-->
    <!--        SELECT-->
    <!--        DATE_FORMAT(collect_date, '%Y-%m') AS collectDate,-->
    <!--        SUM(radiant_quantity) AS totalRadiantQuantity-->
    <!--        FROM-->
    <!--        bto_radiant_quantity-->
    <!--        <where>-->
    <!--            <if test="query.weatherStationUidList != null and query.weatherStationUidList.size() > 0">-->
    <!--                AND station_uid IN-->
    <!--                <foreach collection="query.weatherStationUidList" item="item" open="(" close=")" separator=",">-->
    <!--                    #{item}-->
    <!--                </foreach>-->
    <!--            </if>-->

    <!--            <if test="query.startTime != null and query.startTime != '' and query.endTime != null and query.endTime != ''">-->
    <!--                AND collect_date BETWEEN #{query.startTime} AND #{query.endTime}-->
    <!--            </if>-->
    <!--        </where>-->
    <!--        GROUP BY-->
    <!--        collectDate-->
    <!--        ORDER BY-->
    <!--        collectDate DESC-->

    <!--    </select>-->

    <select id="getRadiationByDateMonth" resultType="java.util.Map">
        SELECT
        DATE_FORMAT(collect_date, '%Y-%m') AS collectDate,
        SUM(avg_radiant_quantity) AS totalRadiantQuantity
        FROM
        v_avg_radiant
        <where>
            <if test="query.weatherStationUidList != null and query.weatherStationUidList.size() > 0">
                AND station_uid IN
                <foreach collection="query.weatherStationUidList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="query.cities != null and query.cities.size() > 0">
                AND city IN
                <foreach collection="query.cities" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.startTime != null and query.startTime != '' and query.endTime != null and query.endTime != ''">
                AND collect_date BETWEEN #{query.startTime} AND #{query.endTime}
            </if>
        </where>
        GROUP BY
        collectDate
        ORDER BY
        collectDate DESC

    </select>

    <!--    String getPredictionElectricityByRadiation(@Param("radiation") String radiation);-->
    <select id="getPredictionElectricityByRadiation" resultType="string">
        CALL proc_project_forecast(#{radiation}, #{projectId})
    </select>
</mapper>