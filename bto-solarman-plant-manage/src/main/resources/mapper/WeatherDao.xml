<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.WeatherMapper">

    <resultMap type="com.bto.commons.pojo.entity.WeatherEntity" id="btoWeatherMap">
        <result property="city" column="city"/>
        <result property="time" column="time"/>
        <result property="weather" column="weather"/>
        <result property="temp" column="temp"/>
        <result property="wind" column="wind"/>
        <result property="windscale" column="windscale"/>
        <result property="windspeed" column="windspeed"/>
        <result property="humidity" column="humidity"/>
        <result property="precip" column="precip"/>
        <result property="pressure" column="pressure"/>
        <result property="cloud" column="cloud"/>
        <result property="dew" column="dew"/>
        <result property="updateTime" column="update_time"/>
        <result property="wind360" column="wind360"/>
    </resultMap>



    <select id="selectList" resultType="com.bto.commons.pojo.entity.WeatherEntity">
        SELECT
            city,
            weather,
            temp,
            humidity,
            cloud,
            `time`
        FROM
            ${tableName}
        WHERE
            city = #{city}
            AND
            `time` BETWEEN #{start}
                AND #{end}
        ORDER BY
            `time` ASC
    </select>

    <select id="selectWeatherRadiation" resultType="com.bto.commons.pojo.vo.WeatherRadiationVO">
        SELECT `time`,
               weather,
               avg_radiation
        FROM bto_weather_radiation
        WHERE `time` IN (SELECT
            TIME ( `time` )
        FROM
            ${tableName}
        WHERE
            city = #{city}
          AND `time` BETWEEN #{start}
          AND #{end}
            )
    </select>

    <select id="getHistorySevenDaysWeather" resultType="com.bto.commons.pojo.vo.WeatherFutureVO">
        SELECT
            MAX( cast(temp AS SIGNED ) ) tempMax,
            MIN( cast(temp AS SIGNED ) ) tempMin,
            windscale,
            wind,
            weather,
            DATE( time ) collectDate
        FROM
            ${tableName}
        WHERE
            city = #{city}
          AND `time` BETWEEN #{start}
          AND #{end}
        GROUP BY
            collectDate
    </select>
    <select id="getRadiationByCity" resultType="java.lang.Double">
        WITH w_weather
            AS ( SELECT
                     weather
                 FROM ${tableName}
                 WHERE time LIKE #{date}
        AND city = #{city}
        GROUP BY weather
        ORDER BY COUNT(*) DESC LIMIT 2 ) SELECT
            sum( bw.radiation )/(
            count( ww.weather )) AS radiation
        FROM
            w_weather ww
            LEFT JOIN
                bto_weather_radiation_1 bw ON ww.weather = bw.weather
    </select>
    <select id="getWeatherByPlantUid" resultType="com.bto.commons.pojo.entity.WeatherEntity">
        SELECT
            city,
            `time`,
            weather,
            temp,
            wind,
            windscale,
            windspeed,
            humidity,
            pressure,
            cloud,
            dew,
            update_time,
            wind360
        FROM
            bto_weather.${tableName}
        WHERE
                city = ( SELECT city FROM bto_photovoltaic.bto_plant_base WHERE plant_uid = '5FD4BD12-5CA0-480B-9266-08CFD52A722F' )
          AND `time` > NOW()
        ORDER BY
            `time` ASC
            LIMIT 1
    </select>

</mapper>