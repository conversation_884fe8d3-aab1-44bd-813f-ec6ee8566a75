<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.plant.dao.SensorDao">
    <select id="page" resultType="com.bto.commons.pojo.entity.SensorEntity">
        WITH t_rank AS (SELECT bsd.*,bdv.install_address,
        RANK() OVER ( PARTITION BY bsd.sensor_id ORDER BY bsd.create_time DESC ) AS `rank`
        FROM bto_sensor_data
        bsd LEFT JOIN bto_device bdv ON bsd.sensor_id = bdv.device_id
        )
        SELECT *
        FROM t_rank
        <where>
            AND `rank` = 1
            <if test="query.sensorId!=null and query.sensorId.size()>0">
                and sensor_id IN
                <foreach collection="query.sensorId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY create_time desc
    </select>

</mapper>