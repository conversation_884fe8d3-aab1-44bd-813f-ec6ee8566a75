<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.WeatherRadiationMapper">

    <resultMap type="com.bto.commons.pojo.entity.WeatherRadiationEntity" id="weatherRadiationMap">
        <result property="time" column="time"/>
        <result property="weather" column="weather"/>
        <result property="maxRadiation" column="max_radiation"/>
        <result property="minRadiation" column="min_radiation"/>
        <result property="avgRadiation" column="avg_radiation"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="getRadiationByWeather" resultType="java.lang.Double">
        SELECT
            avg_radiant_quantity
        FROM
            v_weather_radiant
        WHERE
            weather = #{weather}
    </select>
    <select id="getHistoryRadiationByWeather" resultType="com.bto.commons.pojo.vo.DateRadiationVO">
        WITH w_weather AS (
            SELECT
                weather,
            DATE( time ) date
        FROM
            ${tableName}
        WHERE
            DATE( time ) = #{localDate}
          AND city = #{city}
        GROUP BY
            weather
        ORDER BY
            COUNT(*) DESC
            LIMIT 2
            ) SELECT
                  DATE( ww.date ) date,
                  sum( bw.radiation )/(
                  count( ww.weather )) AS radiation
              FROM
                  w_weather ww
                  LEFT JOIN bto_weather_radiation_1 bw ON ww.weather = bw.weather
    </select>

</mapper>