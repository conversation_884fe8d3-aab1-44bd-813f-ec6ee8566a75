<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.CityMapDao">

    <resultMap type="com.bto.commons.pojo.entity.CityMapEntity" id="cityMapMap">
        <result property="id" column="id"/>
        <result property="pid" column="pid"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="mapId" column="map_id"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>
    <select id="getParentAndChild" resultType="com.bto.commons.pojo.vo.CityMapVO">
        SELECT *
        FROM bto_city_map
        <where>
            AND bto_city_map.is_deleted = '0'
            <if test="cities != null and cities.size() > 0">
                AND bto_city_map.`name` IN
                <foreach collection="cities" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>

        UNION ALL

        SELECT parent.*
        FROM bto_city_map AS child
        JOIN bto_city_map AS parent ON child.pid = parent.id
        <where>
            AND parent.is_deleted = '0'
            AND child.is_deleted = '0'
            <if test="cities != null and cities.size() > 0">
                AND child.`name` IN
                <foreach collection="cities" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>