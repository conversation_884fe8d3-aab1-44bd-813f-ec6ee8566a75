<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.MeteorologyMapper">

    <resultMap type="com.bto.commons.pojo.entity.MeteorologyEntity" id="btoMeteorologyMap">
        <result property="stationUid" column="station_uid"/>
        <result property="solarRadiation" column="solar_radiation"/>
        <result property="windSpeed" column="wind_speed"/>
        <result property="windDirection" column="wind_direction"/>
        <result property="windDegree" column="wind_degree"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <select id="selectRadiationByStationIds" resultType="java.lang.Double">
        SELECT
        SUM( radiant_quantity )
        FROM
        bto_radiant_quantity
        WHERE
        station_uid IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        AND `collect_date` BETWEEN #{start}
        AND #{end}
    </select>
    <select id="getHistoryRadiationList" resultType="com.bto.commons.pojo.vo.DateRadiationVO">
        SELECT
        avg_radiant_quantity radiation,
        collect_date `date`,
        city
        FROM
        v_avg_radiant
        <where>
            <if test="city !=null and city.trim() != ''">
                city like concat('%',#{city},'%')
            </if>

            <if test="dates!=null and dates.size()>0">
                AND collect_date IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
            </if>
        </where>

    </select>

</mapper>