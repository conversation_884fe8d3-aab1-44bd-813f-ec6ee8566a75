<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.ForecastMapper">

    <resultMap type="com.bto.commons.pojo.vo.BatteryDivinerVO" id="btoForecastMap">
        <result property="collectDate" column="collect_date"/>
        <result property="electricity" column="predict_electricity"/>
    </resultMap>

    <resultMap id="Map1" type="com.bto.commons.pojo.vo.RegionalElectricityVO">
        <result property="electricity" column="electricity"/>
        <result property="radiantQuantity" column="radiant_quantity"/>
        <result property="predictQuantity" column="predict_quantity"/>
        <result property="collectDate" column="collect_date"/>
        <result property="predictElectricity" column="predict_electricity"/>
    </resultMap>
    <resultMap id="Map2" type="com.bto.commons.pojo.vo.PredictRegionalElectricityVO">
        <result property="collectDate" column="collect_date"/>
        <result property="futureElectricity" column="future_electricity"/>
        <result property="futureRadiant" column="future_radiant"/>
    </resultMap>

    <resultMap type="com.bto.commons.pojo.vo.PlantPowerVO" id="forecastPowerMap">
        <result property="initTime" column="init_time"/>
        <result property="power" column="predict_power"/>
    </resultMap>

    <resultMap type="com.bto.commons.pojo.vo.RealityForecastElectricityVO" id="realityForecastElectricityMap">
        <result property="electricity" column="electricity"/>
        <result property="plantUid" column="plant_uid"/>
        <result property="collectDate" column="collect_date"/>
        <result property="electricity" column="predict_electricity"/>
    </resultMap>

    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>

    <select id="getPlantForecastData" resultMap="btoForecastMap">
        CALL proc_predict_electricity (#{plantId})
    </select>
    <select id="getFutureForecastData" resultType="java.lang.String">
        CALL proc_predict_electricity_future (#{plantId},#{radiation})
    </select>
    <select id="getWeatherStationUid" resultType="java.lang.String">
        SELECT fun_station_uid(#{plantId}) as stationUid
    </select>
    <select id="getFutureForecastByCity" resultType="java.lang.String">
        CALL proc_regional_forecast(#{city},#{cityRadiation})
    </select>
    <select id="getForecastPlantList" resultType="com.bto.commons.pojo.vo.ForecastPlantVO">
        SELECT
        plant_uid,
        city,
        plant_name,
        plant_status,
        today_electricity / 100 todayElectricity,
        fun_predict_electricity ( plant_uid ) / 100 forecastElectricity
        FROM
        bto_plant_base bpb
        <where>
            and plant_uid in (
            SELECT
            plant_uid
            FROM
            bto_plant_info
            WHERE
            is_deleted = 0
            <include refid="userInfo"/>
            )
            <if test="query.plantName != '' and query.plantName != null">
                AND plant_name like CONCAT('%', #{query.plantName}, '%')
            </if>
            <if test="query.allCity != null and query.allCity.size() > 0">
                AND city IN
                <foreach collection="query.allCity" item="city" open="(" close=")" separator=",">
                    #{city}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCurrentForecastElectricity" resultType="java.lang.String">
        SELECT fun_predict_electricity(#{plantId}) / 100
    </select>
    <select id="getPredictPower" resultMap="forecastPowerMap">
        CALL proc_predict_power (#{plantId},#{date})
    </select>
    <select id="getRealityForecastElectricity" resultType="com.bto.commons.pojo.vo.RealityForecastElectricityVO">
        select
            plant_uid,
            collect_date,
            electricity / 100 electricity,
            predict_electricity / 100 predict_electricity,
            ROUND((electricity / NULLIF(predict_electricity, 0)) * 100., 2) AS evaluate
        from
            v_predict_electricity
        <where>
            <if test="query.plantId !='' and query.plantId != null">
                and plant_uid = #{query.plantId}
            </if>
            <if test="query.startDate != null and query.startDate != '' and query.endDate != null and query.endDate != ''">
                and collect_date between #{query.startDate} and #{query.endDate}
            </if>
        </where>
    </select>

    <select id="getPredictElectricityByCity" statementType="CALLABLE" resultMap="Map1,Map2">
        CALL proc_regional_forecast (#{city})
    </select>
    <select id="getForecastPlantListByCity" resultType="com.bto.commons.pojo.vo.ForecastPlantVO">
        select
            v_predict_electricity.plant_uid,
            v_predict_electricity.collect_date,
            bpb.city,
            bpb.plant_name,
            bpb.plant_status,
            v_predict_electricity.electricity / 100 todayElectricity,
            v_predict_electricity.predict_electricity / 100 forecastElectricity
        from
            v_predict_electricity
                left join
            bto_plant_base bpb
                on
            v_predict_electricity.plant_uid = bpb.plant_uid
        <where>
            and v_predict_electricity.plant_uid in (
            SELECT
            plant_uid
            FROM
            bto_plant_info
            WHERE
            is_deleted = 0
            <include refid="userInfo"/>
            )
            <if test="query.plantName != '' and query.plantName != null">
                AND plant_name like CONCAT('%', #{query.plantName}, '%')
            </if>
            <if test="query.allCity != null and query.allCity.size() > 0">
                AND city IN
                <foreach collection="query.allCity" item="city" open="(" close=")" separator=",">
                    #{city}
                </foreach>
            </if>
            and v_predict_electricity.collect_date = #{query.date}
        </where>
    </select>

</mapper>