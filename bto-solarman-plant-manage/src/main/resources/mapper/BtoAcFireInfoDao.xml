<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.BtoAcFireInfoDao">

    <resultMap type="com.bto.commons.pojo.entity.BtoAcFireInfoEntity" id="btoAcFireInfoMap">
        <result property="id" column="id"/>
        <result property="inverterSn" column="inverter_sn"/>
        <result property="initTime" column="init_time"/>
        <result property="acAlarmInfo" column="ac_alarm_info"/>
        <result property="acAlarmInfoCount" column="ac_alarm_info_count"/>
        <result property="batIndex" column="bat_index"/>
        <result property="groupId" column="group_id"/>
        <result property="acHumidity" column="ac_humidity"/>
        <result property="acTemp" column="ac_temp"/>
        <result property="aerosol" column="aerosol"/>
        <result property="aerosolContact" column="aerosol_contact"/>
        <result property="airSwitchAuxiliaryContact" column="air_switch_auxiliary_contact"/>
        <result property="audibleVisualAlarm" column="audible_visual_alarm"/>
        <result property="bianzhi" column="bianzhi"/>
        <result property="cm2FireFightingAlarmCount" column="cm2_fire_fighting_alarm_count"/>
        <result property="cm2FireFightingAlarmList" column="cm2_fire_fighting_alarm_list"/>
        <result property="cm2FireStatus" column="cm2_fire_status"/>
        <result property="cm2LiquidCoolingAlarmCount" column="cm2_liquid_cooling_alarm_count"/>
        <result property="cm2LiquidCoolingAlarmList" column="cm2_liquid_cooling_alarm_list"/>
        <result property="coConc" column="co_conc"/>
        <result property="coLithiumIonSensor" column="co_lithium_ion_sensor"/>
        <result property="coSensorValue" column="co_sensor_value"/>
        <result property="compressorStatus" column="compressor_status"/>
        <result property="condensTemp" column="condens_temp"/>
        <result property="defrostTemp" column="defrost_temp"/>
        <result property="diacContactor" column="diac_contactor"/>
        <result property="diStatus" column="di_status"/>
        <result property="doacContactor" column="doac_contactor"/>
        <result property="doStatus" column="do_status"/>
        <result property="estopAuxiliaryContact" column="estop_auxiliary_contact"/>
        <result property="electricHeatingStatus" column="electric_heating_status"/>
        <result property="exdProtectInfo" column="exd_protect_info"/>
        <result property="exdProtectInfoCount" column="exd_protect_info_count"/>
        <result property="highPressureAlarmLock" column="high_pressure_alarm_lock"/>
        <result property="ifCmDeviceV2" column="if_cm_device_v2"/>
        <result property="ifCmpDevice" column="if_cmp_device"/>
        <result property="indoorFanStatus" column="indoor_fan_status"/>
        <result property="ipMode" column="ip_mode"/>
        <result property="lastOnlineTime" column="last_online_time"/>
        <result property="lciBackwaterTags" column="lci_backwater_tags"/>
        <result property="lciCompressorRunHours" column="lci_compressor_run_hours"/>
        <result property="lciFanSpeed" column="lci_fan_speed"/>
        <result property="lciHeatingRunHours" column="lci_heating_run_hours"/>
        <result property="lciWorkStatus" column="lci_work_status"/>
        <result property="onlineStatus" column="online_status"/>
        <result property="outdoorFanStatus" column="outdoor_fan_status"/>
        <result property="outletTemp" column="outlet_temp"/>
        <result property="positiveRelay" column="positive_relay"/>
        <result property="negativeRelay" column="negative_relay"/>
        <result property="prechargeRelay" column="precharge_relay"/>
        <result property="release" column="release"/>
        <result property="sbAuxiliaryContact" column="sb_auxiliary_contact"/>
        <result property="sensorHumidity" column="sensor_humidity"/>
        <result property="sensorTemp" column="sensor_temp"/>
        <result property="smokeSensor" column="smoke_sensor"/>
        <result property="tempSensor" column="temp_sensor"/>
        <result property="tempSensor1Value" column="temp_sensor1_value"/>
        <result property="tempSensor2Value" column="temp_sensor2_value"/>
        <result property="travelSwitch" column="travel_switch"/>
        <result property="waterImmersionSensor" column="water_immersion_sensor"/>
        <result property="cm2LciStatus" column="cm2_lci_status"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>