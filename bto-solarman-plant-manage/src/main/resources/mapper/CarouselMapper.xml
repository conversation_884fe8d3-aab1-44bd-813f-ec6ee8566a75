<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bto.plant.dao.CarouselMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>

    <select id="getRankWorkEfficiency" resultType="com.bto.commons.pojo.vo.WorkEfficiencyVO">
        SELECT
        bpb.plant_uid,
        device_id inverterId,
        plant_name plantName,
        (power/plant_capacity) as workEfficiency
        FROM bto_plant_base bpb
        JOIN bto_device bd
        ON bpb.plant_uid = bd.plant_uid AND bd.device_type = 1
        <where>
            bpb.is_deleted = 0 AND bd.is_deleted = 0
            <include refid="userInfo"/>
            <if test="query.cities!=null and query.cities.size()>0">
                and bpb.city in
                <foreach collection="query.cities" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY workEfficiency
        <if test="query.sort == 'desc'">
            DESC
        </if>
        limit #{query.limitSize}
    </select>
    <select id="selectPlantList" parameterType="com.bto.commons.pojo.vo.RequireParamsDTO"
            resultType="com.bto.commons.pojo.vo.PlantCarouselVO">
        SELECT bpb.plant_name plantName,
        bpb.city,
        bpb.power,
        bpb.plant_capacity plantCapacity,
        bpb.total_electricity/100 totalElectricity,
        bpb.year_electricity/100 yearElectricity,
        bpb.month_electricity/100 monthElectricity,
        bpb.today_electricity/100 todayElectricity,
        (0.000997 * bpb.total_electricity)/100 co2,
        bpb.create_time createTime,
        bpb.receive_time updateTime,
        bpb.plant_status status,
        bu.user_name userName
        FROM bto_plant_base bpb LEFT JOIN bto_user bu ON bpb.user_uid = bu.user_uid
        <where>
            bpb.is_deleted = '0'
            <!-- <if test="userInfo.roleID =='2314000001'.toString()"> -->
            <!--     AND EXISTS ( SELECT plant_uid FROM `bto_plant_base` WHERE power = 0 AND project_special LIKE  '1%' AND plant_uid = bpb.plant_uid ) IS FALSE -->
            <!-- </if> -->
            <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
                AND bpb.plant_uid IN
                <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                    #{plantUid}
                </foreach>
            </if>
            <if test="userInfo.projectList!=null and userInfo.projectList.size()>0 and userInfo.userType =='1'.toString()">
                AND bpb.project_special IN
                <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                    #{projectID}
                </foreach>
            </if>
        </where>
        ORDER BY createTime DESC
    </select>
    <select id="getPlantCoordinate" resultType="com.bto.commons.pojo.vo.PlantCoordinateInfoVO">
        SELECT plant_uid plantUid,
        plant_name plantName,
        plant_uid plantUid,
        plant_status plantStatus,
        longitude,
        latitude
        FROM bto_plant_base bpb
        <where>
            is_deleted = 0
            <include refid="userInfo"/>
            <if test="query.country!=null and query.country != ''">
                and country = #{query.country}
            </if>
            <if test="query.province!=null and query.province != ''">
                and province = #{query.province}
            </if>
            <if test="query.city!=null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.area!=null and query.area != ''">
                and area = #{query.area}
            </if>
            <if test="query.town!=null and query.town != ''">
                and town = #{query.town}
            </if>
        </where>
    </select>
    <select id="getPlantInfoCoordinateOfArea" resultType="com.bto.commons.pojo.vo.AreaCoordinateInfoVO">
        SELECT
        <if test="query.country!=null and query.country!=''">
            bpb.province area,
            count(bpb.province) plantNum,
        </if>
        <if test="query.province!=null and query.province!=''">
            bpb.city area,
            count(bpb.city) plantNum,
        </if>
        <if test="query.city!=null and query.city!=''">
            bpb.area area,
            count(bpb.area) plantNum,
        </if>
        <if test="query.area!=null and query.area!=''">
            bpb.town area,
            count(bpb.town) plantNum,
        </if>
        t2.longitude,
        t2.latitude
        FROM bto_plant_base bpb
        <if test="userInfo != null and userInfo.userType == '0'.toString()">
            LEFT JOIN bto_user_has_plant t3 ON bpb.plant_uid = t3.plant_uid
        </if>
        LEFT JOIN bto_province_city t2 ON
        <if test="query.country!=null and query.country!=''">
            bpb.city =t2.address
        </if>
        <if test="query.province!=null and query.province!=''">
            bpb.city = t2.address
        </if>
        <if test="query.city!=null and query.city!=''">
            bpb.area = t2.address
        </if>
        <if test="query.area!=null and query.area!=''">
            bpb.town = t2.address
        </if>
        <where>
            bpb.is_deleted = 0
            <if test="query.country!=null and query.country!=''">
                AND bpb.country = #{query.country}
                <choose>
                    <when test="userInfo != null and userInfo.userType == '1'.toString()">
                        <include refid="userInfo"/>
                    </when>
                    <otherwise>
                        AND t3.user_uid = #{userInfo.userUid}
                    </otherwise>
                </choose>
                GROUP BY bpb.city
            </if>
            <if test="query.province!=null and query.province!=''">
                AND bpb.province = #{query.province}
                <choose>
                    <when test="userInfo != null and userInfo.userType == '1'.toString()">
                        <include refid="userInfo"/>
                    </when>
                    <otherwise>
                        AND t3.user_uid = #{userInfo.userUid}
                    </otherwise>
                </choose>
                GROUP BY bpb.city
            </if>
            <if test="query.city!=null and query.city!=''">
                AND bpb.city = #{query.city}
                <choose>
                    <when test="userInfo != null and userInfo.userType == '1'.toString()">
                        <include refid="userInfo"/>
                    </when>
                    <otherwise>
                        AND t3.user_uid = #{userInfo.userUid}
                    </otherwise>
                </choose>
                GROUP BY bpb.area
            </if>
            <if test="query.area!=null and query.area!=''">
                AND bpb.area = #{query.area}
                <choose>
                    <when test="userInfo != null and userInfo.userType == '1'.toString()">
                        <include refid="userInfo"/>
                    </when>
                    <otherwise>
                        AND t3.user_uid = #{userInfo.userUid}
                    </otherwise>
                </choose>
                GROUP BY bpb.town
            </if>
        </where>
    </select>

</mapper>