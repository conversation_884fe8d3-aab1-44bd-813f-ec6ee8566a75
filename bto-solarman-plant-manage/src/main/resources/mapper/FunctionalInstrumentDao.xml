<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.FunctionalInstrumentDao">

    <resultMap type="com.bto.commons.pojo.entity.FunctionalInstrumentEntity" id="functionalInstrumentMap">
        <result property="deviceId" column="device_id"/>
        <result property="electricity" column="electricity"/>
        <result property="initTime" column="init_time"/>
        <result property="apv" column="apv"/>
        <result property="bpv" column="bpv"/>
        <result property="cpv" column="cpv"/>
        <result property="aac" column="aac"/>
        <result property="bac" column="bac"/>
        <result property="cac" column="cac"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="roundFields">
        round
            (apv / 100, 2)
            apv,
        round(bpv / 100, 2) bpv,
        round(cpv / 100, 2) cpv,
        round(aac / 100, 2) aac,
        round(bac / 100, 2) bac,
        round(cac / 100, 2) cac
    </sql>
    <select id="page" resultType="com.bto.commons.pojo.entity.FunctionalInstrumentEntity">
        WITH t_rank AS (SELECT *,
        RANK() OVER ( PARTITION BY device_id ORDER BY init_time DESC ) AS `rank`
        FROM bto_functional_instrument)
        SELECT
        device_id,
        round(electricity / 100, 2) electricity,
        init_time collect_time,
        create_time,
        update_time,
        <include refid="roundFields"/>
        FROM t_rank
        <where>
            AND `rank` = 1
            <if test="query.deviceId!=null and query.deviceId.size()>0">
                and device_id IN
                <foreach collection="query.deviceId" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY electricity desc
    </select>

</mapper>