<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.plant.dao.CustomerContractMapper">
    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>
    <insert id="addBtoContractNumber">
        update bto_contract_info
        set bto_contract_info.bto_contract_id = #{query.btoContractId},
        <if test="query.meterId != null and query.meterId != ''">
            bto_contract_info.meter_id = #{query.meterId},
        </if>
        <if test="query.plantName != null and query.plantName != ''">
            bto_contract_info.plant_name = #{query.plantName},
        </if>
        <if test="query.plantCapacity != null and query.plantCapacity != ''">
            bto_contract_info.installed_capacity = #{query.plantCapacity},
        </if>
        <if test="query.isLocking != null and query.isLocking != ''">
            bto_contract_info.is_locking = #{query.isLocking},
        </if>
        <if test="query.state != null and query.state != ''">
            bto_contract_info.state = #{query.state}
        </if>
        where order_id = #{query.contractId}
    </insert>

    <select id="selectPage" resultType="com.bto.commons.pojo.vo.CustomerContractVO">
        SELECT
        order_id contractId ,
        bto_contract_id,
        meter_id,
        business_company,
        is_locking,
        user_name,
        phone userPhone,
        plant_name,
        installed_capacity plantCapacity,
        address,
        state,
        project_special as project_id,
        order_id,
        create_time,
        update_time
        FROM bto_contract_info bpb
        <where>
            <include refid="userInfo"/>
            <if test="query.state != null and query.state != ''">
                AND state = #{query.state}
            </if>
            <if test="query.address != null and query.address != ''">
                AND address LIKE CONCAT('%', #{query.address}, '%')
            </if>
            <if test="query.projectId != null and query.projectId != ''">
                AND project_id = #{query.projectId}
            </if>
            <if test="query.contractId != null and query.contractId != ''">
                AND contract_id = #{query.contractId}
            </if>
            <if test="query.userName != null and query.userName != ''">
                AND user_name LIKE CONCAT('%', #{query.userName}, '%')
            </if>
            <if test="query.userPhone != null and query.userPhone != ''">
                AND phone = #{query.userPhone}
            </if>
            <if test="query.plantName != null and query.plantName != ''">
                AND plant_name LIKE CONCAT('%', #{query.plantName}, '%')
            </if>
            <if test="query.startCreateTime != null and query.startCreateTime != '' and query.endCreateTime != null and query.endCreateTime != ''">
                AND create_time BETWEEN #{query.startCreateTime} AND #{query.endCreateTime}
            </if>
        </where>
        <choose>
            <when test="query.order != null and query.order != ''">
                ORDER BY ${query.order}
                <if test="query.isAsc != null and query.isAsc">ASC</if>
                <if test="query.isAsc == null or !query.isAsc">DESC</if>
            </when>
            <otherwise>
                ORDER BY create_time
                <if test="query.isAsc != null and query.isAsc">ASC</if>
                <if test="query.isAsc == null or !query.isAsc">DESC</if>
            </otherwise>
        </choose>
    </select>

</mapper>