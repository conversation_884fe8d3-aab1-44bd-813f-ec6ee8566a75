<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.BtoPlantRemarksDao">

    <resultMap type="com.bto.commons.pojo.entity.BtoPlantRemarksEntity" id="btoPlantRemarksMap">
        <result property="plantUid" column="plant_uid"/>
        <result property="remarks" column="remarks"/>
        <result property="isClear" column="is_clear"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <update id="clear">
        UPDATE bto_plant_remarks t1
            JOIN (
            SELECT plant_uid, MAX(create_time) AS max_create_time
            FROM bto_plant_remarks
            WHERE is_clear = 0
            GROUP BY plant_uid
            ) t2 ON t1.plant_uid = t2.plant_uid AND t1.create_time = t2.max_create_time
            SET t1.is_clear = 1
        WHERE t1.is_clear = 0;
    </update>

</mapper>