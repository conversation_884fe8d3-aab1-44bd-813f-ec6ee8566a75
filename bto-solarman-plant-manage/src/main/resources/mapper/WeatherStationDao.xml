<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.WeatherStationDao">

    <resultMap type="com.bto.commons.pojo.entity.WeatherStationEntity" id="weatherStationMap">
        <result property="stationUid" column="station_uid"/>
        <result property="stationName" column="station_name"/>
        <result property="edgServerSn" column="edg_server_sn"/>
        <result property="imei" column="imei"/>
        <result property="deviceAddress" column="device_address"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="town" column="town"/>
        <result property="village" column="village"/>
        <result property="address" column="address"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="remarks" column="remarks"/>
    </resultMap>
    <select id="getAllStationIdByCity" resultType="java.lang.String">
        SELECT
            station_uid
        FROM
            v_weather_station
        WHERE
            city = #{city}
    </select>
    <select id="getWeatherStationByCity" resultType="com.bto.commons.pojo.vo.WeatherStationVO">
        SELECT
            station_uid,
            station_name,
            edg_server_sn,
            imei,
            city,
            area,
            town,
            village,
            address,
            longitude,
            latitude,
            remarks
        FROM
            `bto_weather`.`v_weather_station`
        WHERE
            city = #{city}
    </select>
    <select id="getStationLatestDataByCity" resultType="com.bto.commons.pojo.vo.StationLatestVO">
        SELECT
            vws.station_uid,
            vws.station_name,
            bm.solar_radiation,
            bm.wind_speed,
            bm.wind_direction,
            bm.wind_degree,
            bm.update_time,
            brq.radiant_quantity
        FROM
            v_weather_station AS vws
                JOIN ( SELECT station_uid, MAX( update_time ) AS max_update_time FROM bto_meteorology WHERE DATE(update_time) = #{date} GROUP BY station_uid ) AS latest_updates ON vws.station_uid = latest_updates.station_uid
                JOIN bto_meteorology AS bm ON bm.station_uid = latest_updates.station_uid
                AND bm.update_time = latest_updates.max_update_time
                JOIN bto_radiant_quantity AS brq ON brq.station_uid = vws.station_uid
                AND DATE( brq.collect_date ) = CURDATE()
        WHERE
            vws.city in
        <foreach collection="allCity" item="city" open="(" close=")" separator=",">
            #{city}
        </foreach>
        GROUP BY
            vws.station_uid
    </select>
    <select id="getPageList" resultType="com.bto.commons.pojo.entity.WeatherStationEntity">
        SELECT city FROM v_weather_station GROUP BY city
    </select>
    <select id="getCustomStation" resultType="com.bto.commons.pojo.vo.StationLatestVO">
        WITH r AS (
            SELECT
                station_uid,
                solar_radiation,
                wind_speed,
                wind_direction,
                wind_degree,
                update_time,
                revise_time,
                ROW_NUMBER() OVER (PARTITION BY station_uid ORDER BY update_time DESC) AS row_num
            FROM
                bto_meteorology
            <where>
                station_uid in
                <foreach collection="idList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </where>
        )
        SELECT
            r.station_uid,
            station_name,
            city,
            r.solar_radiation,
            r.wind_speed,
            r.wind_direction,
            r.wind_degree,
            r.update_time
        FROM
            r
        LEFT JOIN bto_weather_station t1 ON  t1.station_uid = r.station_uid
        WHERE
            row_num = 1;

    </select>

    <select id="create" resultType="string">
        CALL proc_create_ws(#{plantName}, #{operatorId})
    </select>

</mapper>