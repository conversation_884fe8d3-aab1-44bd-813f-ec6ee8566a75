<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.plant.dao.PlantEvaluationMapper">

    <sql id="userInfo">
        <if test="userInfo.plantList!=null and userInfo.plantList.size()>0">
            and bpb.plant_uid in
            <foreach collection="userInfo.plantList" item="plantUid" open="(" close=")" separator=",">
                #{plantUid}
            </foreach>
        </if>
        <if test="userInfo.projectList!=null and userInfo.projectList.size()>0">
            and bpb.project_special in
            <foreach collection="userInfo.projectList" item="projectID" open="(" close=")" separator=",">
                #{projectID}
            </foreach>
        </if>
    </sql>


    <select id="statics" resultType="com.bto.commons.pojo.vo.PlantEvaluationStaticsVO">
    SELECT
    CAST(SUM(bpb.today_electricity)/100 AS DECIMAL(12, 2)) AS todayElectricity,
    CAST(SUM(bpb.month_electricity)/100 AS DECIMAL(12, 2)) AS monthElectricity,
    CAST(SUM(bpb.year_electricity)/100 AS DECIMAL(12, 2)) AS yearElectricity
    From v_user_plant bpb
    <where>
        <include refid="userInfo"/>
        <if test="query.projectId !='' and query.projectId !=null">
            and bpb.project_special like concat(#{query.projectId},'%')
        </if>
        <if test="plantUid !='' and plantUid !=null">
            and bpb.plant_uid = #{plantUid}
        </if>
    </where>
</select>

    <!--void callPredictElectricity(List<String> plantUidList);-->
    <select id="callPredictElectricity" resultType="com.bto.commons.pojo.vo.PlantComplianceRateVO">
        CALL proc_predict_electricity(#{plantUid})
    </select>


</mapper>