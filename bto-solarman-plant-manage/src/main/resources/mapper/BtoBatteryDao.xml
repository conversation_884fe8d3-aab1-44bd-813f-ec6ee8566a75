<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bto.plant.dao.BtoBatteryDao">
    <update id="updateWorkMode">
        UPDATE bto_set_parameter
        set
        <if test="workModeConfigDTO.accUserMode == 2">
            charge_list = #{workModeConfigDTO.chargeListJsonStr},
            discharge_list = #{workModeConfigDTO.dischargeListJsonStr},
        </if>
        acc_user_mode = #{workModeConfigDTO.accUserMode}
        WHERE inverter_sn = #{workModeConfigDTO.deviceSn}
    </update>
    <update id="passivePowerSet">
        UPDATE bto_set_parameter
        SET
            passive_grid_charge_power = #{passivePowerDTO.passiveGridChargePower},
            passive_grid_discharge_power = #{passivePowerDTO.passiveGridDisChargePower}
        WHERE
            inverter_sn = #{passivePowerDTO.deviceSn}
    </update>
    <update id="backModeSOCSet">
        UPDATE bto_set_parameter
        SET
            back_mod_soc_retain = #{socRetainDTO.backModSOCRetain}
        WHERE
            inverter_sn = #{socRetainDTO.deviceSn}
    </update>
    <update id="passiveModeSet">
        UPDATE bto_set_parameter
        SET
            passive_mode_enable = #{passiveModeDTO.passiveModeEnable}
        WHERE
            inverter_sn = #{passiveModeDTO.deviceSn}
    </update>

    <select id="getSocAnalyze" resultType="com.bto.commons.pojo.vo.EnergySocAnalyzeVO">
        SELECT
            JSON_UNQUOTE(JSON_EXTRACT( battery_group_datalist, '$[0].batSOC' )) AS soc,
            init_time time
        FROM
            ${tableName}
        <where>
            inverter_sn = #{inverterSn}
        </where>
    </select>

    <select id="chart" resultType="com.bto.commons.pojo.vo.BatteryVO">
        SELECT inverter_sn,
        init_time,
        systotal_loadwatt,
        CASE
        WHEN bat_power &gt; 0 THEN bat_power ELSE 0
        END AS dischargePower,
        CASE
        WHEN bat_power &lt; 0 THEN ABS(bat_power) ELSE 0
        END AS chargePower
        from ${tableName}
        <where>
            AND DATE_FORMAT(init_time, '%Y-%m-%d') = #{query.initTime}
            <if test="query.inverterSn != null and query.inverterSn.size() > 0">
                AND inverter_sn IN
                <foreach collection="query.inverterSn" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY init_time ASC
    </select>
    <select id="getWorkModeParam" resultType="com.bto.commons.pojo.entity.BtoSetParameter">
        SELECT
            plant_uid,
            inverter_sn,
            back_mod_soc_retain backModSOCRetain,
            passive_mode_enable,
            passive_grid_charge_power,
            passive_grid_discharge_power,
            acc_user_mode,
            charge_list chargeListJsonStr,
            discharge_list dischargeListJsonStr,
            update_time
        FROM
            bto_set_parameter
        WHERE
            inverter_sn = #{deviceSn}
            AND is_deleted = 0
    </select>
</mapper>