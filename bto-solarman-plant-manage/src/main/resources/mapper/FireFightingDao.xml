<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--mapper类全包名-->
<mapper namespace="com.bto.plant.dao.FireFightingDao">
    <select id="page" resultType="com.bto.commons.pojo.entity.FireFightingEntity">
        WITH t_rank AS (SELECT *,
        RANK() OVER ( PARTITION BY `id`, `region` ORDER BY `time` DESC ) AS `rank`
        FROM bto_fire_fighting)
        SELECT *
        FROM t_rank
        <where>
            AND `rank` = 1
            <if test="query.id!=null and query.id.size()>0">
                and id IN
                <foreach collection="query.id" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY `time` desc
    </select>


</mapper>