package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.CounterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 配电柜管理控制器
 * 
 * 负责配电柜的查询和分页管理
 * <AUTHOR>
 * @since 2024/10/7 15:45
 */
@RestController
@RequestMapping("counter")
@RequiredArgsConstructor
@Api(tags = "配电柜管理")
public class CounterController {
    public final CounterService counterService;

    /**
     * 分页查询配电柜信息
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    @GetMapping("page")
    @ApiOperation(value = "配电柜分页")
    public Result<PageResult<CounterVO>> list(CounterQuery query) {
        return Result.success(counterService.page(query));
    }
}
