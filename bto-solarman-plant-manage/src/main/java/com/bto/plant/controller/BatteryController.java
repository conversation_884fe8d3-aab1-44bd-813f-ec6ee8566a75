package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.BatteryQuery;
import com.bto.commons.pojo.vo.BatteryInfoVO;
import com.bto.commons.pojo.vo.BatteryVO;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.BtoBatteryInfoService;
import com.bto.plant.service.BtoBatteryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 储能逆变器实时数据控制器
 * <AUTHOR>
 * @since 2025-02-13
 */
@RestController
@RequestMapping("battery")
@Api(tags = "储能逆变器实时数据")
@AllArgsConstructor
public class BatteryController {
    private final BtoBatteryService batteryService;

    private final BtoBatteryInfoService batteryInfoService;

    @PostMapping("chart")
    @ApiOperation("功率分析图")
    public Result<List<BatteryVO>> chart(@RequestBody BatteryQuery query) {
        List<BatteryVO> result = batteryService.chart(query);
        return Result.success(result);
    }

    @PostMapping("energyAnalysis")
    @ApiOperation("能量分析图")
    public Result<List<BatteryVO>> energyAnalysis(@RequestBody BatteryQuery query) {
        List<BatteryVO> result = batteryService.energyAnalysis(query);
        return Result.success(result);
    }

    @PostMapping("getBatteryGuardInfo")
    @ApiOperation("获取电池卫士信息")
    public Result<List<BtoAcFireInfoVO>> getBatteryGuardInfo(@RequestBody BatteryQuery query) {
        List<BtoAcFireInfoVO> result = batteryService.getBatteryGuardInfo(query);
        return Result.success(result);
    }

    @GetMapping("getBatteryClusterInfo")
    @ApiOperation("获取电池簇信息")
    public Result<BatteryInfoVO> getBatteryClusterInfo(@RequestParam("inverterSn") String inverterSn) {
        BatteryInfoVO result = batteryInfoService.getBatteryClusterInfo(inverterSn);
        return Result.success(result);
    }

}