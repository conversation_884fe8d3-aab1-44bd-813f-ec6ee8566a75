package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.SensorQuery;
import com.bto.commons.pojo.entity.SensorEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.utils.DateUtils;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.SensorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 温湿度烟感控制器
 *
 * 负责温湿度烟感数据的管理和查询操作
 * <AUTHOR>
 * @since 2024-10-07 16:20:52
 */
@RestController
@RequestMapping("sensor")
@RequiredArgsConstructor
@Api(tags = "温湿度烟感数据管理")
public class SensorController {
    public final SensorService sensorService;

    /**
     * 分页查询温湿度烟感数据
     * 
     * @param query 查询条件
     * @return 温湿度烟感数据分页结果
     */
    @GetMapping("page")
    @ApiOperation(value = "温湿度烟感数据分页")
    public Result<PageResult<SensorEntity>> list(SensorQuery query) {
        return Result.success(sensorService.page(query));
    }


    /**
     * 获取温湿度烟感实时数据
     * 
     * @param sensorId 传感器ID
     * @param date 日期
     * @return 温湿度烟感实时数据列表
     */
    @GetMapping("{sensorId}")
    @ApiOperation(value = "温湿度烟感实时数据")
    public Result<List<SensorEntity>> detail(@PathVariable("sensorId") String sensorId,
                                             @RequestParam("date") @DateTimeFormat(pattern = DateUtils.DATE_PATTERN) Date date)
    {
        List<SensorEntity> res = sensorService.detail(sensorId, date);
        return Result.success(res);
    }
}
