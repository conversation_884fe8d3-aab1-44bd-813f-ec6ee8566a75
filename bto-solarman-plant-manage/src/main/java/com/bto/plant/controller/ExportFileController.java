package com.bto.plant.controller;

import com.alibaba.excel.EasyExcel;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.CustomerContractQueryDTO;
import com.bto.commons.pojo.dto.ExportElectricityMeterBillDTO;
import com.bto.commons.pojo.dto.PlantExportFileDTO;
import com.bto.commons.pojo.vo.PlantDigestVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.service.ExportFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 文件导出控制器
 * 
 * 负责各种数据导出功能，包括电站列表、客户合同、电表账单等Excel报表导出
 */
@RestController
@Api(tags = "文件导出模块")
@RequestMapping("export")
public class ExportFileController {
    @Autowired
    private ExportFileService exportFileService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    /**
     * 导出电站列表数据-Excel报表
     * 
     * @param query 查询条件
     * @param response 响应对象
     * @throws IOException IO异常
     * @throws IllegalAccessException 非法访问异常
     */
    @PostMapping(value = "/exportPlantList", produces = "application/octet-stream")
    @ApiOperation("电站列表数据-Excel报表导出")
    @OperateLog(module = "电站管理", operateName = "导出电站列表", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportPlantList(@RequestBody PlantExportFileDTO query, HttpServletResponse response) throws IOException, IllegalAccessException {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantDigestVO> plantDigestVOList = exportFileService.exportPlantList(userInfo, query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + "电站列表" + ".xlsx");
        try {
            EasyExcel.write(response.getOutputStream(), PlantDigestVO.class).sheet("电站列表").doWrite(plantDigestVOList);
        } catch (Exception e) {
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED);
        }
    }

    /**
     * 导出客户合同信息-Excel表格
     * 
     * @param query 查询条件
     * @param response 响应对象
     */
    @PostMapping("exportCustomerContract")
    @ApiOperation(value = "导出客户合同信息--EXCEL表格")
    @OperateLog(module = "电站管理", operateName = "导出客户合同信息", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportCustomerContract(@RequestBody CustomerContractQueryDTO query, HttpServletResponse response) {
        exportFileService.customerContractService(query, response);
    }

    /**
     * 导出电表账单
     * 
     * @param response 响应对象
     * @param query 查询条件
     * @throws IOException IO异常
     */
    @GetMapping("/exportElectricityMeterBill")
    @OperateLog(module = "电站管理", operateName = "导出电表账单", type = OperateTypeEnum.REQUEST_EXPORT)
    public void exportWithTemplate(HttpServletResponse response, ExportElectricityMeterBillDTO query) throws IOException {
        exportFileService.exportWithTemplate(query, response);
    }

}