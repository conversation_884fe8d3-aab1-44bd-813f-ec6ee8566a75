package com.bto.plant.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.constant.PlantStatusEnum;
import com.bto.commons.constant.PowerDistributorEnum;
import com.bto.commons.pojo.dto.PlantQueryDTO;
import com.bto.commons.pojo.dto.PlantUpdateDTO;
import com.bto.commons.pojo.dto.PowerPlantInfoQueryDTO;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.EntityUtils;
import com.bto.commons.utils.ResultUtil;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.service.PlantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电站管理控制器
 *
 * <AUTHOR>
 * @date 2023/3/30 17:36
 */
@RefreshScope
@RestController
@RequestMapping("plantManage")
@Api(tags = "电站管理模块")
public class PlantController {
    @Autowired
    private PlantService plantService;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    @GetMapping("/plantDetail/{plantUid}")
    @ApiOperation(value = "根据电站编号查询电站详情信息")
    public Result<PlantVO> getPlantDetail(@PathVariable String plantUid) {
        PlantVO result = plantService.getPlantDetail(plantUid);
        return Result.success(result);
    }

    @ApiOperation(value = "获取电站列表", produces = "application/json", consumes = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "currentPage", value = "当前页", paramType = "query", defaultValue = "1", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "页面大小", paramType = "query", defaultValue = "100", example = "100"),
            @ApiImplicitParam(name = "sortType", value = "排序方式", paramType = "query", defaultValue = "0", example = "0")
    })
    @RequestMapping(path = "/plants", method = RequestMethod.POST)
    public Result getPlants(
            @RequestBody com.bto.commons.pojo.dto.PlantVO plantVO,
            @RequestParam(required = false, defaultValue = "1", value = "currentPage") String startPage,
            @RequestParam(required = false, defaultValue = "100", value = "pageSize") String pageSize,
            @RequestParam(required = false, defaultValue = "1", value = "sortType") String sortType
    ) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<com.bto.commons.pojo.dto.PlantVO> plantPage = plantService.getPlants(plantVO, startPage, pageSize, sortType, userInfo);
        List<com.bto.commons.pojo.dto.PlantVO> records = plantPage.getRecords();
        ArrayList<com.bto.commons.pojo.dto.PlantVO> plantVOList = new ArrayList<>();
        if (records.size() > 0) {
            for (com.bto.commons.pojo.dto.PlantVO record : records) {
                record.setPlantStatus(PlantStatusEnum.getNameByCode(record.getPlantStatus()));
                record.setPlantTypeId(plantService.getPlantTypeNameByPlantTypeId(record.getPlantTypeId()));
                if ("0".equals(String.valueOf(record.getPlantCapacity()))) {
                    record.setDailyEfficiencyPerHour("装机容量异常");
                    record.setDailyEfficiencyPerHour("装机容量异常");
                    record.setPowerDistributor("装机容量异常");
                    plantVOList.add(record);
                    continue;
                }
                BigDecimal todayElectricity = new BigDecimal(record.getTodayElectricity()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal yearElectricity = new BigDecimal(record.getYearElectricity()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal plantCapacity = new BigDecimal(record.getPlantCapacity()).divide(new BigDecimal("1000"), 3, BigDecimal.ROUND_HALF_UP);
                BigDecimal totalCapacity = new BigDecimal(record.getTotalElectricity()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal dailyEfficiencyPerHour = todayElectricity.divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal yearlyEfficiencyPerHour = yearElectricity.divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP);
                record.setTotalElectricity(totalCapacity.toString());
                record.setPlantCapacity(plantCapacity.toString());
                record.setTodayElectricity(todayElectricity.toString());
                record.setYearElectricity(yearElectricity.toString());
                record.setDailyEfficiencyPerHour(dailyEfficiencyPerHour.toString());
                record.setYearlyEfficiencyPerHour(yearlyEfficiencyPerHour.toString());
                record.setPowerDistributor(PowerDistributorEnum.getNameByCode(record.getPowerDistributor()));
                plantVOList.add(record);
            }
            JSONObject jsonObject = ResultUtil.assembleDataWithTotal(plantVOList, Long.valueOf(plantPage.getTotal()).intValue(), Long.valueOf(plantPage.getCurrent()).intValue(), Long.valueOf(plantPage.getSize()).intValue());
            return Result.success(jsonObject);
        }
        return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "没有查询到任何电站");
    }

    @ApiOperation("电站树查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "plantName", value = "电站名称", paramType = "query", example = "东莞大岭山卢培武"),
            @ApiImplicitParam(name = "currentPage", value = "当前页", defaultValue = "1", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "页面大小", defaultValue = "100", paramType = "query", example = "100")
    })
    @RequestMapping(path = "/plantTree", method = RequestMethod.GET)
    public Result getPlantTree(
            @RequestParam(required = false, value = "plantName") String plantName,
            @RequestParam(required = false, defaultValue = "1", value = "currentPage") String startPage,
            @RequestParam(required = false, defaultValue = "100", value = "pageSize") String pageSize
    ) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Page<Plant> plantPage = plantService.getPlantTree(plantName, startPage, pageSize, userInfo);
        List<Plant> records = plantPage.getRecords();
        if (records != null) {
            ArrayList<Map<String, String>> plantTreeVOList = new ArrayList<>();
            for (Plant record : records) {
                String recordPlantName = record.getPlantName();
                String recordPlantUid = record.getPlantUid();
                HashMap<String, String> temp = new HashMap<>();
                temp.put("plantUid", recordPlantUid);
                temp.put("plantName", recordPlantName);
                plantTreeVOList.add(temp);
            }
            // 数据拼装,返回一个带有电站名称和Uid信息树，total值，当前页，页的大小的data属性
            JSONObject jsonObject = ResultUtil.assembleDataWithTotal(plantTreeVOList, Long.valueOf(plantPage.getTotal()).intValue(), Long.valueOf(plantPage.getCurrent()).intValue(), Long.valueOf(plantPage.getSize()).intValue());
            return Result.success(jsonObject);
        }
        return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), ResultUtil.assembleDataWithTotal(new JSONArray(), 0, 1, 100));
    }

    @ApiOperation("电站详情查询")
    @ApiImplicitParams({@ApiImplicitParam(name = "plantUid", value = "电站Uid", paramType = "path", required = true)})
    @RequestMapping(path = "/plantInfo/{plantUid}", method = RequestMethod.GET)
    public Result getPlantInfo(@PathVariable(name = "plantUid") String plantUid) {
        com.bto.commons.pojo.dto.PlantVO plantVO = plantService.getPlantInfo(plantUid);
        if (plantVO != null) {
            return Result.success(plantVO);
        }
        return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "没有查询到该电站信息");
    }


    @ApiOperation("拿plantUid到plant模块兑换plantname --api接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "plantUid", value = "电站Uid", paramType = "path", required = true)
    })
    @RequestMapping(value = "plantName/{plantUid}", method = RequestMethod.GET)
    public Result<String> getPlantNameByPlantUid(@PathVariable("plantUid") String plantUid) {
        String plantName = plantService.getPlantNameByPlantUid(plantUid);
        if (plantName != null && !"".equals(plantName)) {
            return Result.success(plantName);
        }
        return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "没有查询到电站名称");
    }

    @ApiOperation("获取某月的电站电量统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "plantUid", value = "电站Uid", paramType = "path", required = true, example = "AC0657A2-AD20-4838-8624-5083AA182380"),
            @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", required = true, example = "2023-05-01 00:00:00"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", required = true, example = "2023-05-31 00:00:00")
    })
    @RequestMapping(value = "/plantElectricityByMonth/{plantUid}", method = RequestMethod.POST)
    public Result getPlantElectricityStatsByMonth(
            @PathVariable("plantUid") String plantUid,
            @RequestParam(name = "startTime") String startTime,
            @RequestParam(name = "endTime") String endTime) {
        List<HashMap<String, String>> plantElectricityStatsByMonthList = plantService.getPlantElectricityStatsByMonth(plantUid, startTime, endTime);
        if (plantElectricityStatsByMonthList.size() >= 1) {
            return Result.success(plantElectricityStatsByMonthList);
        }
        return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "没有查询到电站的月发电量");
    }

    @ApiOperation("获取某年电站发电量统计")
    @ApiImplicitParams({@ApiImplicitParam(name = "plantUid", value = "电站Uid", paramType = "path", required = true), @ApiImplicitParam(name = "startTime", value = "开始时间", paramType = "query", required = true), @ApiImplicitParam(name = "endTime", value = "结束时间", paramType = "query", required = true)})
    @RequestMapping(value = "/plantElectricityByYear/{plantUid}", method = RequestMethod.POST)
    public Result getPlantElectricityStatsByYear(@PathVariable("plantUid") String plantUid, @RequestParam(name = "startTime") String startTime, @RequestParam(name = "endTime") String endTime) {
        List<HashMap<String, BigDecimal>> plantElectricityStatsByYearList = plantService.getPlantElectricityStatsByYear(plantUid, startTime, endTime);
        if (plantElectricityStatsByYearList.size() >= 1) {
            return Result.success(plantElectricityStatsByYearList);
        }
        return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "没有查询到电站的年发电量");
    }

    @ApiOperation("通过plantUid获取电站信息")
    @GetMapping("getPlantInfoByPlantUid/{plantUid}")
    public Result getPlantInfoByPlantUid(@PathVariable("plantUid") String plantUid) {
        Plant plantInfo = plantService.getPlantInfoByPlantUid(plantUid);
        if (EntityUtils.isEntityEmpty(plantInfo)) {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "");
        } else {
            return Result.success(plantInfo);
        }

    }

    @ApiOperation("更新电站信息")
    @PostMapping("/updatePlant")
    @OperateLog(module = "电站管理", operateName = "更新电站信息", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result updatePlantInfo(@RequestBody PlantUpdateDTO plantUpdateDTO) {
        Integer result = plantService.updatePlantInfo(plantUpdateDTO);
        if (result > 0) {
            return Result.success("电站信息更新成功");
        }
        return Result.instance(ResultEnum.PLANT_NOT_EXIST.getCode(), ResultEnum.PLANT_NOT_EXIST.getMessage(), "电站信息更新失败");
    }


    @PostMapping("/getPlantList")
    @ApiOperation("获取电站列表")
    public Result<PlantListPageVO<PlantInfoVO>> getPlantList(@RequestBody PlantQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PlantListPageVO<PlantInfoVO> plantListCountResultVO = plantService.getPlantList(query, userInfo);
        if (plantListCountResultVO.getTotal() > 0) {
            List<PlantInfoVO> records = plantListCountResultVO.getRecords();
            records.forEach(
                    record -> {
                        String todayElectricity = record.getTodayElectricity();
                        String yearElectricity = record.getYearElectricity();
                        String plantCapacity = record.getPlantCapacity();
                        record.setProjectName(globalParamUtil.getProjectNameByProjectId(record.getProjectId()));
                        record.setProjectCompany(globalParamUtil.getProjectNameByProjectId(record.getProjectId()));
                        record.setDailyEfficiencyPerHour(BusinessCalculateUtil.getRealEfficiencyPerHours(todayElectricity, plantCapacity));
                        record.setYearlyEfficiencyPerHour(BusinessCalculateUtil.getRealEfficiencyPerHours(yearElectricity, plantCapacity));
                    }
            );
            plantListCountResultVO.setRecords(records);
            return Result.success(plantListCountResultVO);
        } else {
            return Result.instance(ResultEnum.NO_CONTENT, plantListCountResultVO);
        }
    }

    @PostMapping("/engineer/getPlantList")
    @ApiOperation("工程师获取电站列表")
    public Result<PlantListPageVO<Plant>> engineerGetPlantList(@RequestBody PlantQueryDTO query) {
        PlantListPageVO<Plant> plantListCountResultVO = plantService.engineerGetPlantList(query);
        return Result.success(plantListCountResultVO);
    }

    @PostMapping("/selectAll")
    @ApiOperation("获取所有电站id和名称")
    public Result<List<PlantIdWithNameVO>> selectAll(@RequestParam(required = false) String plantName) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantIdWithNameVO> list = plantService.selectAll(userInfo, plantName);
        if (list.size() == 0) {
            return Result.instance(ResultEnum.NO_CONTENT, list);
        }

        return Result.success(list);
    }

    @ApiOperation("收藏电站")
    @PostMapping("/collectPlant")
    @OperateLog(module = "电站管理", operateName = "收藏电站", type = OperateTypeEnum.REQUEST_INSERT)
    public Result collectPlant(String userUid, String plantUid) {
        Integer result = plantService.collectPlant(userUid, plantUid);
        return result > 0 ? Result.success("收藏电站成功") : Result.instance(ResultEnum.COLLECT_PLANT_FAIL);
    }

    @ApiOperation("通过plantName获取电站信息")
    @PostMapping("getPlantInfoByPlantName")
    public Result getPlantInfoByPlantName(@NotNull(message = "电站名不能为空") @RequestParam(value = "plantName") String plantName) {
        Plant plantInfo = plantService.getPlantInfoByPlantName(plantName);
        if (EntityUtils.isEntityEmpty(plantInfo)) {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(), "");
        } else {
            return Result.success(plantInfo);
        }

    }

    @ApiOperation("发电效率报表分页")
    @PostMapping("getPowerPlantInfoByLocation")
    public Result<IPage<PowerPlantInfoVO>> getPowerPlantInfoByLocation(@RequestBody PowerPlantInfoQueryDTO query) {
        return Result.success(plantService.getPowerPlantInfoByLocation(query));
    }

    @ApiOperation("发电量排名分页")
    @PostMapping("getPlantElectricityRank")
    public Result<IPage<WorkEfficiencyVO>> getPlantElectricityRank(@RequestBody PowerPlantInfoQueryDTO query) {
        return Result.success(plantService.getPlantElectricityRank(query));
    }

    @ApiOperation("导出发电效率报表Excel")
    @PostMapping("getPowerPlantInfoByLocationExcel")
    @OperateLog(module = "电站管理", operateName = "导出发电效率报表Excel", type = OperateTypeEnum.REQUEST_EXPORT)
    public void getPowerPlantInfoByLocationExcel(@RequestBody PowerPlantInfoQueryDTO query, HttpServletResponse response) {
        plantService.getPowerPlantInfoByLocationExcel(query, response);
    }

    @ApiOperation("注册电站")
    @PostMapping("registerPlant")
    @OperateLog(module = "电站管理", operateName = "注册电站", type = OperateTypeEnum.REQUEST_INSERT)
    public Result<Plant> registerPlant(@RequestBody Plant plant) {
        return Result.success(plantService.registerPlant(plant));
    }
}
