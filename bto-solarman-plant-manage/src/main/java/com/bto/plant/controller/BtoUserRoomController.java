package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.BtoUserRoomQuery;
import com.bto.commons.pojo.vo.BtoUserRoomVO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import com.bto.plant.service.BtoUserRoomService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 空间管理控制器
 * 
 * 负责用户空间的管理，包括空间查询、新增、修改和删除等操作
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
@RestController
@RequestMapping("spaceManagement")
@Api(tags = "空间管理")
@AllArgsConstructor
public class BtoUserRoomController {
    private final BtoUserRoomService btoUserRoomService;

    /**
     * 获取用户所有空间
     * 
     * @param userUid 用户UID
     * @return 空间列表
     */
    @PostMapping("list/{userUid}")
    @ApiOperation("获取所有空间")
    public Result<List<BtoUserRoomVO>> list(@PathVariable String userUid){
        List<BtoUserRoomVO> list = btoUserRoomService.list(userUid);
        return Result.success(list);
    }

    /**
     * 新增或修改用户空间
     * 
     * @param vo 空间信息
     * @return 操作结果
     */
    @PostMapping("saveOrUpdate")
    @ApiOperation("新增/修改空间")
    @OperateLog(module = "空间管理", operateName = "新增或修改用户空间", type = OperateTypeEnum.REQUEST_INSERT)
    public Result handleSaveOrUpdate(@RequestBody BtoUserRoomVO vo){
        btoUserRoomService.handleSaveOrUpdate(vo);
        return Result.success();
    }

    /**
     * 删除用户空间
     * 
     * @param query 删除条件
     * @return 操作结果
     */
    @DeleteMapping("delete")
    @ApiOperation("删除空间")
    @OperateLog(module = "空间管理", operateName = "删除用户空间", type = OperateTypeEnum.REQUEST_DELETE)
    public Result delete(@RequestBody BtoUserRoomQuery query){
        btoUserRoomService.delete(query);
        return Result.success();
    }
}