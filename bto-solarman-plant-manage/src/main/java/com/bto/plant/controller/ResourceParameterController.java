package com.bto.plant.controller;

import com.bto.commons.pojo.entity.ResourcesParameter;
import com.bto.commons.response.Result;
import com.bto.plant.service.ResourceParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 资源管理控制器
 * 
 * 负责地区资源数据的管理和查询操作
 * <AUTHOR>
 * @since 2024/1/7 14:45
 */
@Slf4j
@RefreshScope
@RestController
@AllArgsConstructor
@RequestMapping("resourceParameter")
@Api(tags = "资源管理模块")
public class ResourceParameterController {
    private final ResourceParameterService resourceParameterService;

    /**
     * 根据城市获取地区资源数据
     * 
     * @param city 城市名称
     * @return 地区资源数据
     */
    @ApiOperation("获取地区资源数据")
    @GetMapping
    public Result getInfoByCity(@NotNull String city) {
        ResourcesParameter entity = resourceParameterService.getInfoByCity(city);
        return Result.success(entity);
    }

}
