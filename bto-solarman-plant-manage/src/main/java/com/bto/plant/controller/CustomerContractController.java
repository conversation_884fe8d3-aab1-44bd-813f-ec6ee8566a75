package com.bto.plant.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.CustomerContractQueryDTO;
import com.bto.commons.pojo.vo.CustomerContractInfoVO;
import com.bto.commons.pojo.vo.CustomerContractVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.ObjectUtils;
import com.bto.commons.utils.PoiUtils;
import com.bto.logs.annotations.OperateLog;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.service.CustomerContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 客户合同控制器
 * 
 * 负责客户合同信息的管理，包括合同查询、新增、批量导入、合同编号修改等操作
 * <AUTHOR>
 * @date 2023/8/8 14:53
 */
@RestController
@RequestMapping("/CustomerContract")
@Api(tags = "客户合同信息管理")
public class CustomerContractController {
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Autowired
    private CustomerContractService customerContractService;

    /**
     * 客户合同信息查询
     * 
     * @param query 查询条件
     * @return 合同信息分页结果
     */
    @ApiOperation("客户合同信息查询")
    @PostMapping("/info")
    public Result<IPage<CustomerContractVO>> getCustomerContractInfo(@RequestBody CustomerContractQueryDTO query){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<CustomerContractVO> page = customerContractService.getCustomerContractInfo(query, userInfo);
        if (page.getTotal()>0){
            return Result.success(page);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT,page);
        }
    }
    /**
     * 新增客户合同信息
     * 
     * @param contractInfo 合同信息
     * @return 操作结果
     * @throws IllegalAccessException 非法访问异常
     */
    @ApiOperation("新增客户合同信息")
    @PostMapping("/addInfo")
    @OperateLog(module = "客户合同信息管理", operateName = "新增客户合同信息", type = OperateTypeEnum.REQUEST_INSERT)
    public Result addCustomerContractInfo(@RequestBody CustomerContractInfoVO contractInfo) throws IllegalAccessException {
        if (ObjectUtils.objectParamsIsNull(contractInfo)){
            return Result.instance(ResultEnum.REQUEST_PARAM_NULL_ERROR);
        }
        customerContractService.addCustomerContractInfo(contractInfo);
        return Result.success(ResultEnum.OPERATION_SUCCESS);
    }

    /**
     * 批量插入客户合同信息
     * 
     * @param contractInfoList 合同信息列表
     * @return 操作结果
     * @throws IllegalAccessException 非法访问异常
     * @throws InterruptedException 中断异常
     */
    @ApiOperation("批量插入客户合同信息")
    @PostMapping("/batchInsert")
    @OperateLog(module = "客户合同信息管理", operateName = "批量插入客户合同信息", type = OperateTypeEnum.REQUEST_INSERT)
    public Result<String> batchInsertCustomerContractInfo(@RequestBody List<CustomerContractInfoVO> contractInfoList) throws IllegalAccessException, InterruptedException {
        if (CollUtil.isEmpty(contractInfoList)){
            return Result.instance(ResultEnum.DATA_NULL_ERROR,"");
        }
        Integer count  = customerContractService.batchInsertCustomerContractInfo(contractInfoList);
        if (count<=0){
            return Result.instance(ResultEnum.BATCH_INSERT_ERROR);
        }
        return Result.success(ResultEnum.OPERATION_SUCCESS,"成功导入"+count+"条数据");
    }


    /**
     * 项目excel文件导入
     * 
     * @param excelFile Excel文件
     * @return 导入数据
     * @throws IOException IO异常
     */
    @PostMapping("/uploadExcel")
    @ApiOperation(value = "项目excel文件导入")
    @OperateLog(module = "客户合同信息管理", operateName = "导入客户合同Excel文件", type = OperateTypeEnum.REQUEST_IMPOERT)
    public Result uploadExcel(@RequestParam("file") MultipartFile excelFile) throws IOException {
        ExcelReader excelReader = ExcelUtil.getReader(excelFile.getInputStream());
        List<Object> objectList = PoiUtils.getObjectList(excelReader);
        return Result.success(objectList);
    }

    /**
     * 修改博通合同编号
     * 
     * @param query 查询条件
     * @return 操作结果
     */
    @PostMapping("/addBtoContractNumber")
    @ApiOperation(value = "修改博通合同编号")
    @OperateLog(module = "客户合同信息管理", operateName = "修改博通合同编号", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result addBtoContractNumber(@RequestBody CustomerContractQueryDTO query) {
        customerContractService.addBtoContractNumber(query);
        return Result.success();
    }



}
