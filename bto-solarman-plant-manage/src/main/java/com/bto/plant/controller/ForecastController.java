package com.bto.plant.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.service.ForecastService;
import com.bto.plant.service.PlantService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * 发电预测控制器
 * 
 * 负责发电预测相关功能，包括气象数据获取、发电预测、收益预测等操作
 * <AUTHOR> by zhb on 2023/12/21.
 */
@RestController
@RequestMapping("forecast")
@Api(tags = "发电预测模块")
@AllArgsConstructor
public class ForecastController {

    private final ForecastService forForecastService;
    private final PlantService plantService;
    private final GlobalParamUtil globalParamUtil;

    /**
     * 获取气象站与天气预报数据
     * 
     * @param plantId 电站ID
     * @return 气象数据
     */
    @ApiOperation("获取气象站与天气预报数据")
    @PostMapping("getWeatherData")
    public Result<WeatherDataVO> getWeatherDatanew(String plantId) {
        return Result.success(forForecastService.getWeatherData(plantId));
    }

    /**
     * 获取预测与实际曲线对比数据
     * 
     * @param plantId 电站ID
     * @param date 日期
     * @return 预测与实际对比数据
     */
    @ApiOperation("获取预测与实际曲线对比数据")
    @PostMapping("getEvaluateChart")
    public Result<HashMap<String, List<PlantPowerVO>>> getEvaluateChart(String plantId, String date) {
        HashMap<String, List<PlantPowerVO>> map = forForecastService.getEvaluateChart(plantId, date);
        return Result.success(map);
    }

    /**
     * 根据市区获取辐射量
     * 
     * @param city 城市名称
     * @return 辐射量数据
     */
    @ApiOperation("根据市区获取辐射量")
    @GetMapping("getRadiationByCity")
    public Result<HashMap<String, List<DateRadiationVO>>> getRadiationByCity(String city) {
        HashMap<String, List<DateRadiationVO>> dateRadiation = forForecastService.getDateRadiationByCity(city);
        return Result.success(dateRadiation);
    }

    /**
     * 根据市区获取近14天天气
     * 
     * @param city 城市名称
     * @return 天气数据
     */
    @ApiOperation("根据市区获取近14天天气")
    @GetMapping("getWeatherByCity")
    public Result<HashMap<String, List<WeatherFutureVO>>> getWeatherByCity(String city) {
        return Result.success(forForecastService.getWeatherByCity(city));
    }

    /**
     * 根据市区获取发电量
     * 
     * @param city 城市名称
     * @return 发电量数据
     */
    @ApiOperation("根据市区获取发电量")
    @GetMapping("getElectricityByCity")
    public Result<HashMap<String, List<BatteryDivinerVO>>> getElectricityByCity(String city) {
        HashMap<String, List<BatteryDivinerVO>> electricityByCity = forForecastService.getElectricityByCity(city);
        // electricityByCity.put("historyRealElectricity", forForecastService.getHistoryElectricityByCity(city));
        return Result.success(electricityByCity);
    }


    /**
     * 预测15天内发电曲线
     * 
     * @param projectId 项目ID
     * @return 发电预测数据
     */
    @ApiOperation("预测15天内发电曲线")
    @GetMapping("predictionElectByProject")
    public Result predictionElectByProject(String projectId) {
        AbstractMap<String, String> map = forForecastService.predictionElectByProject(projectId, 15);
        return Result.success(map);
    }


    /**
     * 预测15天内电站档次划分
     * 
     * @param projectId 项目ID
     * @return 电站档次分析数据
     */
    @ApiOperation("预测15天内电站档次划分")
    @GetMapping("predictionElectAnalysis")
    public Result predictionElectAnalysis(String projectId) {
        Map<String, Integer> map = forForecastService.predictionElectAnalysis(projectId);
        return Result.success(map);
    }


    /**
     * 发电收益预测
     * 
     * @param projectId 项目ID
     * @return 发电收益预测数据
     */
    @ApiOperation("发电收益预测")
    @GetMapping("predictionElectricity")
    public Result predictionElectricity(String projectId) {
        HashMap<String, BigDecimal> map = forForecastService.predictionElectricity(projectId);
        return Result.success(map);
    }

    /**
     * 获取发电等级评估数据
     * 
     * @param query 查询条件
     * @return 发电等级评估数据
     */
    @ApiOperation("获取发电等级评估数据")
    @PostMapping("getGradeEvaluationPlantList")
    public Result<HashMap<String, Object>> getGradeEvaluationPlantList(@RequestBody ForecastPlantDTO query) {
        HashMap<String, Object> map = forForecastService.getGradeEvaluationPlantList(query);
        return Result.success(map);
    }

    /**
     * 分页查询气象站范围内的电站
     * 
     * @param query 查询条件
     * @return 电站分页结果
     */
    @ApiOperation("分页查询气象站范围内的电站")
    @PostMapping("getInStationPlantPage")
    public Result<IPage<PlantBaseInfoVO>> getInStationPlantPage(@RequestBody PlantQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<PlantBaseInfoVO> result = plantService.getInStationPlantPage(userInfo, query);
        return Result.success(result);
    }

    /**
     * 根据电站ID获取指定时间实际发电与预测发电数据
     * 
     * @param realityForecastElectricityDTO 查询条件
     * @return 实际与预测发电数据
     */
    @ApiOperation("根据电站ID获取指定时间实际发电与预测发电数据")
    @PostMapping("getRealityForecastElectricity")
    public Result<List<RealityForecastElectricityVO>> getRealityForecastElectricity(@RequestBody RealityForecastElectricityDTO realityForecastElectricityDTO) {
        List<RealityForecastElectricityVO> list = forForecastService.getRealityForecastElectricity(realityForecastElectricityDTO);
        return Result.success(list);
    }
}
