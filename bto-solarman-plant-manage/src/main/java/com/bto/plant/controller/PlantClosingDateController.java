package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.vo.PlantClosingDateVO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.PlantClosingDateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运维结算控制器
 * 
 * 负责运维结算表的管理，包括新增、删除和查询运维结算记录
 * <AUTHOR>
 * @since 2024-06-21
 */
@RestController
@RequestMapping("closingDate")
@Api(tags = "运维结算管理")
@AllArgsConstructor
public class PlantClosingDateController {
    private final PlantClosingDateService plantClosingDateService;


    /**
     * 新增运维结算记录
     * 
     * @param vo 运维结算信息
     * @return 操作结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增")
    @OperateLog(module = "运维结算管理", operateName = "新增运维结算记录", type = OperateTypeEnum.REQUEST_INSERT)
    public Result save(@RequestBody PlantClosingDateVO vo) {
        plantClosingDateService.save(vo);
        return Result.success();
    }


    /**
     * 删除运维结算记录
     * 
     * @param vo 删除条件
     * @return 操作结果
     */
    @DeleteMapping
    @ApiOperation(value = "删除")
    @OperateLog(module = "运维结算管理", operateName = "删除运维结算记录", type = OperateTypeEnum.REQUEST_DELETE)
    public Result delete(@RequestBody PlantClosingDateVO vo) {
        plantClosingDateService.deleteEntity(vo);
        return Result.success();
    }


    /**
     * 根据电站ID查询运维结算记录
     * 
     * @param plantUid 电站ID
     * @return 运维结算记录列表
     */
    @GetMapping("/{plantUid}")
    @ApiOperation(value = "查询")
    public Result<List<PlantClosingDateVO>> saveOrUpdate(@PathVariable String plantUid) {
        List<PlantClosingDateVO> list = plantClosingDateService.selectByPlantUid(plantUid);
        return Result.success(list);
    }

}