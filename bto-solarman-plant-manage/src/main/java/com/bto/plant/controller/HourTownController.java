package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.HourTownQuery;
import com.bto.commons.pojo.entity.HourTownEntity;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.HourTownService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 市镇发电数据控制器
 * 
 * 负责市-镇月均发电数据的管理和查询操作
 * <AUTHOR>
 * @since 2024/9/28 10:33
 */
@RestController
@RequestMapping("hourTown")
@RequiredArgsConstructor
@Api(tags = "市-镇月均发电")
public class HourTownController {

    public final HourTownService hourTownService;
    /**
     * 根据日期查询市-镇月均发电数据
     * 
     * @param start 开始日期
     * @param end 结束日期
     * @param query 查询条件
     * @return 市镇月均发电数据列表
     */
    @PostMapping("list")
    @ApiOperation(value = "根据日期查询市-镇月均发电")
    public Result<List<HourTownEntity>> list(@RequestParam(value = "start", defaultValue = "yyyy-MM") String start,
                                             @RequestParam(value = "end", defaultValue = "yyyy-MM") String end,
                                             @RequestBody HourTownQuery query
                                             ) {
        return Result.success(hourTownService.list(start,end,query));
    }
}
