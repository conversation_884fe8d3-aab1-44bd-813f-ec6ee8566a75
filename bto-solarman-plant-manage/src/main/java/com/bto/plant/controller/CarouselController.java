package com.bto.plant.controller;

import com.bto.commons.pojo.dto.PlantAddressDTO;
import com.bto.commons.pojo.dto.PlantCarouselDTO;
import com.bto.commons.pojo.dto.RankWorkEfficiencyQueryDTO;
import com.bto.commons.pojo.vo.AreaCoordinateInfoVO;
import com.bto.commons.pojo.vo.PlantCoordinateInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.WorkEfficiencyVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.service.CarouselService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 列表轮播控制器
 * 
 * 负责处理列表轮播相关功能，包括工作效率排行、站点轮播、坐标信息查询等
 * <AUTHOR>
 * @date 2023/4/21 15:45
 */
@RestController
@RequestMapping("carousel")
@Api(tags = "列表轮播模块")
public class CarouselController {

    @Autowired
    private CarouselService carouselService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    /**
     * 获取工作效率排行
     * 
     * @param query 查询条件
     * @return 工作效率排行列表
     */
    @ApiOperation("工作效率排行")
    @PostMapping("rankWorkEfficiency")
    public Result<List<WorkEfficiencyVO>> getRankWorkEfficiency(@RequestBody RankWorkEfficiencyQueryDTO query){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<WorkEfficiencyVO> workEfficiency = carouselService.getRankWorkEfficiency(query,userInfo);
        return Result.success(workEfficiency);
    }


    /**
     * 获取站点轮播数据
     * 
     * @param startIndex 开始索引
     * @param endIndex 结束索引
     * @return 站点轮播数据
     */
    @ApiOperation("站点轮播")
    @GetMapping("getPlantCarousel")
    @ApiImplicitParams({
            //参数效验
            @ApiImplicitParam(name = "startIndex", value = "开始索引", required = true, paramType = "query",defaultValue = "0"),
            @ApiImplicitParam(name = "endIndex", value = "结束索引", required = true, paramType = "query",defaultValue = "50")
    })
    public Result<PlantCarouselDTO> getPlantCarousel(@RequestParam("startIndex") String startIndex, @RequestParam("endIndex") String endIndex){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        PlantCarouselDTO plantCarousel = carouselService.getPlantCarousel(startIndex, endIndex,userInfo);
        if(plantCarousel.getRecords().size()>0){
            return Result.success(plantCarousel);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(),plantCarousel);
        }
    }

    /**
     * 获取电站具体坐标信息
     * 
     * @param plantAddressDTO 电站地址信息
     * @return 电站坐标信息列表
     */
    @ApiOperation("获取电站具体坐标信息")
    @PostMapping("getPlantCoordinate")
    public Result<List<PlantCoordinateInfoVO>> getPlantCoordinate(@RequestBody PlantAddressDTO plantAddressDTO){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<PlantCoordinateInfoVO> plantCoordinateList = carouselService.getPlantCoordinate(plantAddressDTO,userInfo);
            if(plantCoordinateList.size()>0){
                return Result.success(plantCoordinateList);
            }else {
                return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(),plantCoordinateList);
            }
        }

    /**
     * 获取区域电站数量信息
     * 
     * @param plantAddressDTO 电站地址信息
     * @return 区域电站数量信息列表
     */
    @ApiOperation("获取电站数量信息")
    @PostMapping("getPlantInfoCoordinateOfArea")
    public Result<List<AreaCoordinateInfoVO>> getPlantInfoCoordinateOfArea(@RequestBody PlantAddressDTO plantAddressDTO){
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<AreaCoordinateInfoVO> result = carouselService.getPlantInfoCoordinateOfArea(plantAddressDTO,userInfo);
        if(result.size()>0){
            return Result.success(result);
        }else {
            return Result.instance(ResultEnum.NO_CONTENT.getCode(), ResultEnum.NO_CONTENT.getMessage(),result);
        }
    }

}