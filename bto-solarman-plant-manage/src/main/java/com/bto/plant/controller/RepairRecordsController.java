package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.RepairRecordsQuery;
import com.bto.commons.pojo.entity.RepairRecordsEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.RepairRecordsVO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.convert.RepairRecordsConvert;
import com.bto.plant.service.RepairRecordsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 维修记录控制器
 * 
 * 负责维修记录的管理，包括分页查询、详情获取、增删改查等操作
 */
@RestController
@RequestMapping("repairRecords")
@Api(tags = "维修记录")
@AllArgsConstructor
public class RepairRecordsController {
    private final RepairRecordsService repairRecordsService;

    /**
     * 分页查询维修记录
     * 
     * @param query 查询条件
     * @return 维修记录分页结果
     */
    @GetMapping("page")
    @ApiOperation("分页")
    public Result<PageResult<RepairRecordsVO>> page(@Valid RepairRecordsQuery query) {
        PageResult<RepairRecordsVO> page = repairRecordsService.page(query);
        return Result.success(page);
    }

    /**
     * 根据ID获取维修记录详情
     * 
     * @param id 主键ID
     * @return 维修记录详情
     */
    @GetMapping("{id}")
    @ApiOperation("信息")
    public Result<RepairRecordsVO> get(@PathVariable("id") Long id) {
        RepairRecordsEntity entity = repairRecordsService.getById(id);

        return Result.success(RepairRecordsConvert.INSTANCE.convert(entity));
    }

    /**
     * 新增维修记录
     * 
     * @param vo 维修记录信息
     * @return 操作结果
     */
    @PostMapping
    @ApiOperation("保存")
    @OperateLog(module = "维修记录", operateName = "新增维修记录", type = OperateTypeEnum.REQUEST_INSERT)
    public Result<String> save(@RequestBody RepairRecordsVO vo) {
        repairRecordsService.save(vo);

        return Result.success();
    }

    /**
     * 修改维修记录
     * 
     * @param vo 维修记录信息
     * @return 操作结果
     */
    @PutMapping
    @ApiOperation("修改")
    @OperateLog(module = "维修记录", operateName = "修改维修记录", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<String> update(@RequestBody @Valid RepairRecordsVO vo) {
        repairRecordsService.update(vo);

        return Result.success();
    }

    /**
     * 删除维修记录
     * 
     * @param idList ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @ApiOperation("删除")
    @OperateLog(module = "维修记录", operateName = "删除维修记录", type = OperateTypeEnum.REQUEST_DELETE)
    public Result<String> delete(@RequestBody List<Long> idList) {
        repairRecordsService.delete(idList);

        return Result.success();
    }
}