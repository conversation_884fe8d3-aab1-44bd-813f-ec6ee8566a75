package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.CustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * 客户控制器
 * 
 * 负责客户相关功能的管理，包括气象站数据查询等操作
 * <AUTHOR>
 * @since 2024/9/21 17:37
 */
@RestController
@AllArgsConstructor
@RequestMapping("customer/weatherStation")
@Api(tags = "客户模块")
public class CustomerController {
    private final CustomerService customerService;

    /**
     * 获取客户气象站最新数据集合
     * 
     * @return 客户气象站最新数据
     */
    @PostMapping("stationLatest")
    @ApiOperation("获取客户气象站最新数据集合")
    public Result<HashMap<String, Object>> getCustomStation() {
        HashMap<String, Object> result = customerService.getCustomStation();
        return Result.success(result);
    }


}
