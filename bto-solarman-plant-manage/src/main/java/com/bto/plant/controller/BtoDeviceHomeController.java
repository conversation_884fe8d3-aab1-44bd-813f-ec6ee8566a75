package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.BtoDeviceHomeQuery;
import com.bto.commons.pojo.vo.BtoDeviceHomeVO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import com.bto.plant.service.BtoDeviceHomeService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 家居设备控制器
 * 负责家居设备的查询、管理、增删改查等操作
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
@RestController
@RequestMapping("home")
@Api(tags = "家居设备")
@AllArgsConstructor
public class BtoDeviceHomeController {
    private final BtoDeviceHomeService btoDeviceHomeService;

    /**
     * 获取家居设备列表
     * 
     * @param query 查询条件
     * @return 家居设备列表
     */
    @PostMapping("list")
    @ApiOperation("获取全屋/分类家具设备查询")
    public Result<List<BtoDeviceHomeVO>> list(@RequestBody BtoDeviceHomeQuery query) {
        List<BtoDeviceHomeVO> list = btoDeviceHomeService.getList(query);
        return Result.success(list);
    }

    /**
     * 根据设备ID获取设备详情
     * 
     * @param deviceId 设备ID
     * @param userUid 用户UID
     * @return 设备详情信息
     */
    @GetMapping("{deviceId}")
    @ApiOperation("根据设备id获取设备信息")
    public Result<BtoDeviceHomeVO> getDeviceInfo(@PathVariable("deviceId") String deviceId, @RequestParam(value = "userUid", required = true) String userUid) {
        return Result.success(btoDeviceHomeService.getDeviceInfo(deviceId, userUid));
    }

    /**
     * 新增或修改家居设备
     * 
     * @param vo 家居设备信息
     * @return 操作结果
     */
    @PostMapping("saveOrUpdate")
    @ApiOperation("新增/修改")
    @OperateLog(module = "家居设备", operateName = "新增或修改家居设备", type = OperateTypeEnum.REQUEST_INSERT)
    public Result<String> saveOrUpdate(@RequestBody BtoDeviceHomeVO vo) {
        btoDeviceHomeService.handleSaveOrUpdate(vo);
        return Result.success();
    }

    /**
     * 删除家居设备
     * 
     * @param query 删除条件
     * @return 操作结果
     */
    @DeleteMapping
    @ApiOperation("删除")
    @OperateLog(module = "家居设备", operateName = "删除家居设备", type = OperateTypeEnum.REQUEST_DELETE)
    public Result<String> delete(@RequestBody BtoDeviceHomeQuery query) {
        btoDeviceHomeService.delete(query);
        return Result.success();
    }
}