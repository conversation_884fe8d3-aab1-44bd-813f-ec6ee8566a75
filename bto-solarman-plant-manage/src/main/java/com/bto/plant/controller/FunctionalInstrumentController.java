package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.FunctionalInstrumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 多功能仪表控制器
 * 
 * 负责多功能仪表数据的管理和查询操作
 * <AUTHOR>
 * @since  2024-10-09
 */
@RestController
@RequestMapping("functionalInstrument")
@Api(tags="多功能仪表数据")
@AllArgsConstructor
public class FunctionalInstrumentController {
    private final FunctionalInstrumentService functionalInstrumentService;

    /**
     * 分页查询多功能仪表数据
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    @GetMapping("page")
    @ApiOperation("分页")
    public Result<PageResult<FunctionalInstrumentEntity>> page(@Valid FunctionalInstrumentQuery query){
        PageResult<FunctionalInstrumentEntity> page = functionalInstrumentService.page(query);

        return Result.success(page);
    }

}