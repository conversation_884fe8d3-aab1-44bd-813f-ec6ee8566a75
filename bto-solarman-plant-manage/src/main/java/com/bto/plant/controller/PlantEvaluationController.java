package com.bto.plant.controller;

import com.bto.commons.pojo.dto.PlantEvaluationQuery;
import com.bto.commons.pojo.vo.CityMapVO;
import com.bto.commons.pojo.vo.PlantEvaluationStaticsVO;
import com.bto.commons.pojo.vo.ProjectInfoVO;
import com.bto.commons.response.Result;
import com.bto.plant.service.PlantEvaluationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电站评估控制器
 * 
 * 负责电站评估相关功能，包括电站统计、辐射量分析、发电量对比等操作
 * <AUTHOR>
 * @since 2024/1/5 9:37
 */
@Slf4j
@RefreshScope
@RestController
@AllArgsConstructor
@RequestMapping("plantEvaluation")
@Api(tags = "电站评估模块")
public class PlantEvaluationController {
    private final PlantEvaluationService plantEvaluationService;


    /**
     * 电站统计
     * 
     * @param query 查询条件
     * @return 电站统计数据
     */
    @ApiOperation("电站统计")
    @PostMapping("statics")
    public Result statics(PlantEvaluationQuery query) {
        PlantEvaluationStaticsVO vo = plantEvaluationService.statics(query);
        return Result.success(vo);
    }

    /**
     * 获取辐射量和发电量曲线（近30天辐射量与发电量）
     * 
     * @param query 查询条件
     * @return 辐射量和发电量曲线数据
     */
    @ApiOperation("辐射量和发电量曲线/近30天辐射量与发电量")
    @PostMapping("radiationAndElectricity")
    public Result radiationAndElectricity(PlantEvaluationQuery query) {
        List<Map<String, String>> radiationList = plantEvaluationService.getRadiationWithDate(query);
        List<Map<String, String>> electricityList = plantEvaluationService.getElectricityWithDate(query);
        HashMap<String, List<Map<String, String>>> map = new HashMap<>();
        map.put("radiation", radiationList);
        map.put("electricity", electricityList);
        return Result.success(map);
    }

    /**
     * 近3月分析-实际发电与预测发电曲线对比
     * 
     * @param query 查询条件
     * @return 发电量对比分析数据
     */
    @ApiOperation("近3月分析-实际发电与预测发电曲线对比")
    @PostMapping("getElectricityThreeMonthsCompare")
    public Result getElectricityThreeMonthsCompare(PlantEvaluationQuery query) {
        // 实发
        Map<String, String> really = plantEvaluationService.getElectricityThreeMonthsCompare(query);
        // 预发
        Map<String, String> prediction = plantEvaluationService.getPreElectricityByRadiationWithMonth(query);
        HashMap<String, Map<String, String>> map = new HashMap<>();
        map.put("really", really);
        map.put("prediction", prediction);
        return Result.success(map);
    }

    /**
     * 获取预测与实际等效小时数据
     * 
     * @param query 查询条件
     * @return 电站评估信息
     */
    @ApiOperation("预测与实际等效小时")
    @PostMapping("getPlantEvaluationInfo")
    public Result getPlantEvaluationInfo(PlantEvaluationQuery query) {
        Map<String, String> map = plantEvaluationService.getPlantEvaluationInfo(query);
        return Result.success(map);
    }

    /**
     * 获取有气象站的项目树
     * 
     * @return 项目树列表
     */
    @ApiOperation("获取有气象站的项目树")
    @PostMapping("getProjectTreeForWeatherStation")
    public Result getProjectTreeForWeatherStation() {
        List<ProjectInfoVO> list = plantEvaluationService.getProjectTreeForWeatherStation();
        return Result.success(list);
    }

    /**
     * 获取有气象站的资源评估的area
     * 
     * @param projectId 项目ID
     * @return 城市区域树列表
     */
    @ApiOperation("获取有气象站的资源评估的area")
    @PostMapping("getCityAreaTreeWithMapId")
    public Result<List<CityMapVO>> getCityAreaTreeWithMapId(String projectId) {
        List<CityMapVO> tree = plantEvaluationService.getCityAreaTreeWithMapId(projectId);
        return Result.success(tree);
    }


}
