package com.bto.plant.controller;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.logs.annotations.OperateLog;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import io.reactivex.Flowable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * AI聊天机器人控制器
 * 
 * 负责AI对话功能，包括流式聊天接口的实现
 */
@RestController
@RequestMapping("/chatbot")
public class ChatbotController {

    /**
     * 创建线程池
     */
    ThreadPoolExecutor executor = new ThreadPoolExecutor(2, 4, 60, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy()
    );

    /**
     * 实现 chat 接口，支持流式返回数据
     * 
     * @param query 查询内容
     * @return 响应流式数据
     */
    @RequestMapping(value = "/chat", method = RequestMethod.POST)
    @OperateLog(module = "AI对话", operateName = "AI聊天对话", type = OperateTypeEnum.REQUEST_OTHER)
    public ResponseBodyEmitter streamData(@RequestBody String query) {
        ResponseBodyEmitter emitter = new ResponseBodyEmitter(180000L);
        executor.execute(() -> {
            try {
                JsonObject jsonObject = new JsonParser().parse(query).getAsJsonObject();
                streamCall(emitter, jsonObject.get("prompt").getAsString());
            } catch (NoApiKeyException | InputRequiredException e) {
                e.printStackTrace();
                emitter.completeWithError(e);
            }
        });
        return emitter;
    }

    /**
     * 调用百炼应用，封装流式返回数据
     * 
     * @param emitter 响应发射器
     * @param query 查询内容
     * @throws NoApiKeyException 无API密钥异常
     * @throws InputRequiredException 输入参数异常
     */
    public void streamCall(ResponseBodyEmitter emitter, String query) throws NoApiKeyException, InputRequiredException {
        // appId 填入百炼应用 ID
        ApplicationParam param = ApplicationParam.builder()
                .apiKey("sk-97a77dd255b84e2b8932111669932e2f")
                .appId("43dccda3bc7f4279bc444899e10c7052")
                .prompt(query)
                .incrementalOutput(true)
                .build();
        Application application = new Application();
        Flowable<ApplicationResult> result = application.streamCall(param);
        AtomicInteger counter = new AtomicInteger(0);
        result.blockingForEach(data -> {
            int newValue = counter.incrementAndGet();
            String resData = "id:" + newValue + "\nevent:result\n:HTTP_STATUS/200\ndata:" + new Gson().toJson(data) + "\n\n";
            emitter.send(resData.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            if ("stop".equals(data.getOutput().getFinishReason())) {
                emitter.complete();
            }
        });
    }
}