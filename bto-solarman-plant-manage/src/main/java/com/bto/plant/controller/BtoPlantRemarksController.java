package com.bto.plant.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.BtoPlantRemarksDTO;
import com.bto.commons.pojo.entity.BtoPlantRemarksEntity;
import com.bto.commons.pojo.vo.BtoPlantRemarksVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.BtoPlantRemarksService;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 电站备注控制器
 * 
 * 负责电站备注信息的管理，包括分页查询、新增、修改和清除备注等操作
 * <AUTHOR>
 * @since 1.0.0 2024-06-20
 */
@RestController
@RequestMapping("remarks")
@Api(tags = "电站备注模块")
@AllArgsConstructor
public class BtoPlantRemarksController {
    private final BtoPlantRemarksService btoPlantRemarksService;

    /**
     * 分页查询电站备注信息
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("page")
    @Operation(summary = "分页数据")
    public Result<IPage<BtoPlantRemarksEntity>> page(@RequestBody BtoPlantRemarksDTO query){
        IPage<BtoPlantRemarksEntity> page = btoPlantRemarksService.page(query);
        return Result.success(page);
    }

    /**
     * 新增电站备注信息
     * 
     * @param vo 备注信息
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "新增备注信息")
    @OperateLog(module = "电站备注模块", operateName = "新增电站备注", type = OperateTypeEnum.REQUEST_INSERT)
    public Result save(@RequestBody BtoPlantRemarksVO vo){
        btoPlantRemarksService.save(vo);
        return Result.success(ResultEnum.OPERATION_SUCCESS);
    }

    /**
     * 修改电站备注信息
     * 
     * @param vo 备注信息
     * @return 操作结果
     */
    @PutMapping
    @Operation(summary = "修改备注信息")
    @OperateLog(module = "电站备注模块", operateName = "修改电站备注", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result update(@RequestBody @Valid BtoPlantRemarksVO vo){
        btoPlantRemarksService.update(vo);
        return Result.success(ResultEnum.OPERATION_SUCCESS);
    }

    /**
     * 根据电站ID清除最新一条备注信息
     * 
     * @param plantUid 电站ID
     * @return 操作结果
     */
    @PutMapping("clear/{id}")
    @Operation(summary = "根据电站ID清除最新一条备注信息")
    @OperateLog(module = "电站备注模块", operateName = "清除电站备注", type = OperateTypeEnum.REQUEST_DELETE)
    public Result clear(@PathVariable("id") String plantUid){
        btoPlantRemarksService.clear(plantUid);
        return Result.success(ResultEnum.OPERATION_SUCCESS);
    }
}