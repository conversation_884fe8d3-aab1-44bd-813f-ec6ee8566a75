package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.FireFightingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 消防联动控制器
 * 
 * 负责消防联动设备的管理和查询操作
 */
@RestController
@RequestMapping("fireFighting")
@Api(tags = "消防联动管理")
@AllArgsConstructor
public class FireFightingController {
    private final FireFightingService fireFightingService;

    /**
     * 分页查询消防联动信息
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    @GetMapping("page")
    @ApiOperation("分页")
    public Result<PageResult<FireFightingEntity>> page(@Valid FireFightingQuery query) {
        PageResult<FireFightingEntity> page = fireFightingService.page(query);
        return Result.success(page);
    }

}