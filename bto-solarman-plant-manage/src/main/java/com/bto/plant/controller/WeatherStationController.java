package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.WeatherStationQuery;
import com.bto.commons.pojo.entity.WeatherEntity;
import com.bto.commons.pojo.entity.WeatherStationEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.StationLatestVO;
import com.bto.commons.pojo.vo.WeatherStationVO;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.WeatherStationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 气象站控制器
 * 
 * 负责气象站的管理，包括气象站信息查询、创建、运维器修改等操作
 * <AUTHOR>
 * @since 2024/1/6 9:38
 */
@RefreshScope
@RestController
@AllArgsConstructor
@RequestMapping("weatherStation")
@Api(tags = "气象站模块")
public class WeatherStationController {
    private final WeatherStationService weatherStationService;

    /**
     * 分页获取气象站所在市
     * 
     * @param query 查询条件
     * @return 气象站所在市分页结果
     */
    @PostMapping("list")
    @ApiOperation("分页获取气象站所在市")
    public Result<PageResult<String>> getPageList(@RequestBody WeatherStationQuery query) {
        PageResult<String> page = weatherStationService.getPageList(query);
        return Result.success(page);
    }

    /**
     * 根据城市获取气象站信息
     * 
     * @param city 城市名称
     * @return 气象站信息列表
     */
    @PostMapping("getWeatherStationByCity")
    @ApiOperation("根据城市获取气象站信息")
    public Result<List<WeatherStationVO>> getWeatherStationByCity(String city) {
        List<WeatherStationVO> list = weatherStationService.getWeatherStationByCity(city);
        return Result.success(list);
    }

    /**
     * 根据城市获取气象站最新监测数据
     * 
     * @param allCity 城市列表
     * @param date 日期
     * @return 气象站最新监测数据列表
     */
    @PostMapping("getStationLatestDataByCity")
    @ApiOperation("根据城市获取气象站最新监测数据")
    public Result<List<StationLatestVO>> getStationLatestDataByCity(@RequestBody List<String> allCity, @RequestParam(value = "date") String date) {
        List<StationLatestVO> list = weatherStationService.getStationLatestDataByCity(allCity, date);
        return Result.success(list);
    }


    /**
     * 创建气象站
     * 
     * @param plantName 电站名称
     * @param operatorId 运维器ID
     * @return 操作结果
     */
    @GetMapping("create")
    @ApiOperation("创建气象站")
    @OperateLog(module = "气象站模块", operateName = "创建气象站", type = OperateTypeEnum.REQUEST_INSERT)
    public Result<String> create(@RequestParam("plantName") String plantName, @RequestParam("operatorId") String operatorId) {
        weatherStationService.create(plantName, operatorId);
        return Result.success();
    }

    /**
     * 气象站分页查询
     * 
     * @param query 查询条件
     * @return 气象站分页结果
     */
    @PostMapping("engineer/page")
    @ApiOperation("气象站分页")
    public Result<PageResult<WeatherStationEntity>> page(@RequestBody WeatherStationQuery query) {

        return Result.success(weatherStationService.page(query));
    }

    /**
     * 修改气象站运维器
     * 
     * @param stationUid 气象站UID
     * @param newOperatorId 新运维器ID
     * @param oldOperatorId 原运维器ID
     * @return 操作结果
     */
    @GetMapping("engineer/changeOperator")
    @ApiOperation("修改气象站运维器气象站")
    @OperateLog(module = "气象站模块", operateName = "修改气象站运维器", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<String> changeOperator(@RequestParam("stationUid") String stationUid,
                                         @RequestParam("newOperatorId") String newOperatorId,
                                         @RequestParam("oldOperatorId") String oldOperatorId
    ) {
        weatherStationService.changeOperator(stationUid,newOperatorId,oldOperatorId);

        return Result.success();
    }

    /**
     * 根据电站UID获取最新的天气信息
     * 
     * @param plantUid 电站UID
     * @return 最新天气信息
     */
    @GetMapping("getWeatherByPlantUid/{plantUid}")
    @ApiOperation("根据电站UID获取最新的天气信息")
    public Result<WeatherEntity> getWeatherByPlantUid(@PathVariable("plantUid") String plantUid) {
        return Result.success(weatherStationService.getWeatherByPlantUid(plantUid));
    }

}