package com.bto.plant.controller;

import com.bto.commons.pojo.vo.EnergySocAnalyzeVO;
import com.bto.commons.response.Result;
import com.bto.plant.service.StoredEnergyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 储能信息控制器
 * 
 * 负责储能相关功能的管理，包括电池SOC分析等操作
 * <AUTHOR> by bto on 2025/2/12.
 */
@RestController
@RequestMapping("energy")
@RequiredArgsConstructor
@Api(tags = "储能信息")
public class StoredEnergyController {

    private final StoredEnergyService storedEnergyService;

    /**
     * 根据逆变器SN获取电池SOC分析
     * 
     * @param inverterSn 逆变器SN
     * @param dateTime 日期（格式：yyyy-MM-dd）
     * @return 电池SOC分析数据列表
     */
    @GetMapping("getSocAnalyze/{inverterSn}")
    @ApiOperation(value = "根据逆变器SN获取电池SOC分析")
    public Result<List<EnergySocAnalyzeVO>> getSocAnalyze(@PathVariable String inverterSn, @ApiParam("日期：yyyy-MM-dd") @RequestParam("dateTime") String dateTime) {
        return Result.success(storedEnergyService.getSocAnalyze(inverterSn, dateTime));
    }

}
