package com.bto.plant.controller;

import com.bto.commons.pojo.dto.StructuralInspectionQuery;
import com.bto.commons.response.Result;
import com.bto.plant.service.StructuralTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 结构温度检测控制器
 * 
 * 负责结构温度检测数据的管理和查询操作
 * <AUTHOR>
 * @since 1.0.0 2025-06-18
 */
@RestController
@RequestMapping("structural")
@Api(tags = "结构温度检测")
@AllArgsConstructor
public class StructuralTempController {

    private final StructuralTempService structuralTempService;

    @PostMapping("selectList")
    @ApiOperation("结构温度检测历史数据")
    public Result<Object> selectList(@RequestBody StructuralInspectionQuery query) {
        return Result.success(structuralTempService.selectList(query));
    }

    @PostMapping("queryLatest")
    @ApiOperation("根据设备Id获取最新的一条数据")
    public Result<Object> queryLatest(@RequestBody StructuralInspectionQuery query) {
        Object o = structuralTempService.queryLatest(query);
        return Result.success(o);
    }
}