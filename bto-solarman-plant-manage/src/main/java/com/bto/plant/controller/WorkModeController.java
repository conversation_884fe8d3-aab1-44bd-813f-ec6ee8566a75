package com.bto.plant.controller;

import com.alibaba.fastjson.JSONObject;
import com.bto.commons.constant.OperateTypeEnum;
import com.bto.commons.pojo.dto.PassiveModeDTO;
import com.bto.commons.pojo.dto.PassivePowerDTO;
import com.bto.commons.pojo.dto.SOCRetainDTO;
import com.bto.commons.pojo.dto.WorkModeConfigDTO;
import com.bto.commons.pojo.entity.BtoSetParameter;
import com.bto.commons.response.Result;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.service.WorkModeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 储能工作模式控制器
 * 
 * 负责储能工作模式的管理，包括工作模式参数查询、工作模式设置、SOC保留值设置等操作
 * <AUTHOR> by zhb on 2025/4/15.
 */
@RestController
@RequestMapping("work")
@Api(tags = "储能工作模式")
@AllArgsConstructor
@Slf4j
public class WorkModeController {

    private final WorkModeService workModeService;

    /**
     * 查询工作模式控制参数
     * 
     * @param deviceSn 设备SN
     * @return 工作模式控制参数
     */
    @GetMapping("getWorkModeParam")
    @ApiOperation("查询工作模式控制参数")
    public Result<BtoSetParameter> getWorkModeParam(@RequestParam("deviceSn") String deviceSn) {
        BtoSetParameter result = workModeService.getWorkModeParam(deviceSn);
        return Result.success(result);
    }

    /**
     * 设置工作模式及充放电计划
     * 
     * @param workModeConfigDTO 工作模式配置信息
     * @return 设置结果
     */
    @PostMapping("workModeTimeSet")
    @ApiOperation("设置工作模式及充放电计划")
    @OperateLog(module = "储能工作模式", operateName = "设置工作模式及充放电计划", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<JSONObject> workModeTimeSet(@RequestBody WorkModeConfigDTO workModeConfigDTO) {
        JSONObject result = workModeService.workModeTimeSet(workModeConfigDTO);
        return Result.success(result);
    }

    /**
     * 设置后备电池SOC保留值
     * 
     * @param socRetainDTO SOC保留值配置信息
     * @return 设置结果
     */
    @PostMapping("backModeSOCSet")
    @ApiOperation("设置后备电池soc保留值")
    @OperateLog(module = "储能工作模式", operateName = "设置后备电池SOC保留值", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<JSONObject> backModeSOCSet(@RequestBody SOCRetainDTO socRetainDTO) {
        JSONObject result = workModeService.backModeSOCSet(socRetainDTO);
        return Result.success(result);
    }

    /**
     * 设置被动充放电使能
     * 
     * @param passiveModeDTO 被动模式配置信息
     * @return 设置结果
     */
    @PostMapping("passiveModeSet")
    @ApiOperation("设置被动充放电使能")
    @OperateLog(module = "储能工作模式", operateName = "设置被动充放电使能", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<JSONObject> passiveModeSet(@RequestBody PassiveModeDTO passiveModeDTO) {
        JSONObject result = workModeService.passiveModeSet(passiveModeDTO);
        return Result.success(result);
    }

    /**
     * 设置被动充放电功率
     * 
     * @param passivePowerDTO 被动功率配置信息
     * @return 设置结果
     */
    @PostMapping("passivePowerSet")
    @ApiOperation("设置被动充放电功率")
    @OperateLog(module = "储能工作模式", operateName = "设置被动充放电功率", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<JSONObject> passivePowerSet(@RequestBody PassivePowerDTO passivePowerDTO) {
        JSONObject result = workModeService.passivePowerSet(passivePowerDTO);
        return Result.success(result);
    }


}
