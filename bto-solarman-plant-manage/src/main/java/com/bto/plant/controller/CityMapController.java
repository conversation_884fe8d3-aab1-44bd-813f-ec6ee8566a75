package com.bto.plant.controller;

import com.bto.commons.constant.OperateTypeEnum;
import com.bto.logs.annotations.OperateLog;
import com.bto.plant.convert.CityMapConvert;
import com.bto.commons.pojo.entity.CityMapEntity;
import com.bto.commons.pojo.vo.CityMapVO;
import com.bto.commons.response.Result;
import com.bto.plant.service.CityMapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 城市地图控制器
 *
 * 负责城市地图的管理，包括树形结构查询、详情获取、增删改查等操作
 * <AUTHOR>
 * @since 2024-04-17
 */
@RestController
@RequestMapping("cityMap")
@Api(tags = "城市地图模块")
@AllArgsConstructor
public class CityMapController {
    private final CityMapService cityMapService;


    /**
     * 获取城市地图树形结构
     * 
     * @return 城市地图树形列表
     */
    @GetMapping("tree")
    @ApiOperation("树")
    public Result<List<CityMapVO>> tree() {
        List<CityMapVO> list = cityMapService.getTree();

        return Result.success(list);
    }


    /**
     * 根据ID获取城市地图详情
     * 
     * @param id 主键ID
     * @return 城市地图详情
     */
    @GetMapping("{id}")
    @ApiOperation("信息")
    public Result<CityMapVO> get(@PathVariable("id") Long id) {
        CityMapEntity entity = cityMapService.getById(id);

        return Result.success(CityMapConvert.INSTANCE.convert(entity));
    }

    /**
     * 新增城市地图
     * 
     * @param vo 城市地图信息
     * @return 操作结果
     */
    @PostMapping
    @ApiOperation("保存")
    @OperateLog(module = "城市地图模块", operateName = "新增城市地图", type = OperateTypeEnum.REQUEST_INSERT)
    public Result<String> save(@RequestBody CityMapVO vo) {
        cityMapService.save(vo);

        return Result.success();
    }

    /**
     * 修改城市地图
     * 
     * @param vo 城市地图信息
     * @return 操作结果
     */
    @PutMapping
    @ApiOperation("修改")
    @OperateLog(module = "城市地图模块", operateName = "修改城市地图", type = OperateTypeEnum.REQUEST_UPDATE)
    public Result<String> update(@RequestBody @Valid CityMapVO vo) {
        cityMapService.update(vo);

        return Result.success();
    }

    /**
     * 删除城市地图
     * 
     * @param idList ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @ApiOperation("删除")
    @OperateLog(module = "城市地图模块", operateName = "删除城市地图", type = OperateTypeEnum.REQUEST_DELETE)
    public Result<String> delete(@RequestBody List<Long> idList) {
        cityMapService.delete(idList);

        return Result.success();
    }
}