package com.bto.plant.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.converter.vo.PlantClosingDateConvert;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.entity.PlantClosingDateEntity;
import com.bto.commons.pojo.vo.PlantClosingDateVO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.PlantClosingDateDao;
import com.bto.plant.service.PlantClosingDateService;
import com.bto.plant.service.PlantService;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 电站结算日期服务实现类
 * 
 * 电站运维结算日期相关业务服务的具体实现类，提供电站结算日期的增删改查功能。
 *
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Service
@AllArgsConstructor
public class PlantClosingDateServiceImpl extends BaseServiceImpl<PlantClosingDateDao, PlantClosingDateEntity> implements PlantClosingDateService {

    @Resource
    private PlantService plantService;


    /**
     * 保存电站结算日期
     * 
     * 保存新的电站结算日期信息，验证电站存在性并检查日期唯一性。
     * 确保结算日期的唯一性和电站的有效性，为电站运维提供准确的结算周期管理。
     * 
     * @param vo 电站结算日期视图对象，包含电站UID和结算日期信息
     * @throws BusinessException 当电站不存在或结算日期已存在时抛出异常
     */
    @Override
    public void save(PlantClosingDateVO vo) {
        String plantName = plantService.getPlantNameByPlantUid(vo.getPlantUid());
        if (StrUtil.isBlank(plantName)) {
            throw new BusinessException("电站不存在");
        }

        LambdaQueryWrapper<PlantClosingDateEntity> wrapper = getWrapper(vo);
        PlantClosingDateEntity item = baseMapper.selectOne(wrapper);

        if (ObjUtil.isNull(item)) {
            PlantClosingDateEntity entity = new PlantClosingDateEntity();
            entity.setClosingDate(vo.getClosingDate());
            entity.setPlantUid(vo.getPlantUid());
            baseMapper.insert(entity);
        } else {
            throw new BusinessException("结算日期已存在");
        }
    }

    /**
     * 更新电站结算日期
     * 
     * 更新现有的电站结算日期信息，验证数据存在性后执行更新操作。
     * 支持修改已存在的结算日期，确保数据的准确性和时效性。
     * 
     * @param vo 电站结算日期视图对象，包含需要更新的日期信息
     * @throws BusinessException 当电站结算日期不存在时抛出异常
     */
    @Override
    public void updateEntity(PlantClosingDateVO vo) {
        LambdaQueryWrapper<PlantClosingDateEntity> wrapper = getWrapper(vo);

        PlantClosingDateEntity entity = baseMapper.selectOne(wrapper);
        if (ObjUtil.isNull(entity)){
            throw new BusinessException("电站计算日期不存在");
        }
        entity.setClosingDate(vo.getClosingDate());
        baseMapper.update(entity, wrapper);
    }

    /**
     * 构建查询条件包装器
     * 
     * <p>根据视图对象构建MyBatis-Plus查询条件包装器。</p>
     *
     * @param vo 电站结算日期视图对象
     * @return 查询条件包装器
     */
    @NotNull
    private static LambdaQueryWrapper<PlantClosingDateEntity> getWrapper(PlantClosingDateVO vo) {
        LambdaQueryWrapper<PlantClosingDateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(vo.getPlantUid()), PlantClosingDateEntity::getPlantUid, vo.getPlantUid());
        wrapper.eq(ObjUtil.isNotNull(vo.getClosingDate()), PlantClosingDateEntity::getClosingDate, vo.getClosingDate());
        return wrapper;
    }

    /**
     * 删除电站结算日期
     * 
     * <p>删除指定的电站结算日期信息，验证数据存在性后执行删除操作。</p>
     *
     * @param vo 电站结算日期视图对象
     * @throws BusinessException 当电站结算日期不存在或删除失败时抛出异常
     */
    @Override
    public void deleteEntity(PlantClosingDateVO vo) {
        LambdaQueryWrapper<PlantClosingDateEntity> wrapper = getWrapper(vo);
        PlantClosingDateEntity entity = baseMapper.selectOne(wrapper);
        if (ObjUtil.isNull(entity)){
            throw new BusinessException("电站计算日期不存在");
        }

        int deleteRow = baseMapper.delete(wrapper);
        if (deleteRow <= 0) {
            throw new BusinessException(ResultEnum.OPERATION_FAILED);
        }
    }

    /**
     * 根据电站UID查询结算日期列表
     * 
     * <p>根据电站UID查询该电站的所有结算日期信息，按日期降序排列。</p>
     *
     * @param plantUid 电站UID
     * @return 电站结算日期视图对象列表
     */
    @Override
    public List<PlantClosingDateVO> selectByPlantUid(String plantUid) {
        LambdaQueryWrapper<PlantClosingDateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(plantUid), PlantClosingDateEntity::getPlantUid, plantUid)
                .orderByDesc(PlantClosingDateEntity::getClosingDate);
        List<PlantClosingDateEntity> list = baseMapper.selectList(wrapper);
        return PlantClosingDateConvert.INSTANCE.convertList(list);
    }
}