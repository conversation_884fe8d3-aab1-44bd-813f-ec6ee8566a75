package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.pojo.dto.HourTownQuery;
import com.bto.commons.pojo.entity.HourTownEntity;
import com.bto.plant.dao.HourTownDao;
import com.bto.plant.service.HourTownService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 镇村小时数据服务实现类
 * 
 * 镇村小时级气象数据相关业务服务的具体实现类，提供按区域、镇村维度的数据查询功能。
 * 支持按时间范围、区域、镇村等多维度条件查询，为区域气象分析提供数据支持。
 *
 * <AUTHOR>
 * @since 2024/9/28 10:36
 */
@Service
public class HourTownServiceImpl extends ServiceImpl<HourTownDao, HourTownEntity> implements HourTownService {
    /**
     * 查询镇村小时数据列表
     * 
     * 根据时间范围和查询条件获取镇村小时级数据，支持按区域、镇村等多维度条件过滤。
     * 提供数据的分组统计和排序功能，返回结构化的镇村气象数据列表。
     *
     * @param start 开始时间（格式：yyyy-MM-dd）
     * @param end 结束时间（格式：yyyy-MM-dd）
     * @param query 查询条件，包含区域列表和镇村列表筛选参数
     * @return 镇村小时数据实体列表，按区域和镇村分组统计
     */
    @Override
    public List<HourTownEntity> list(String start, String end, HourTownQuery query) {
        List<String> area = query.getArea();
        List<String> town = query.getTown();
        LambdaQueryWrapper<HourTownEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .between(HourTownEntity::getDateMonth, start, end)
                .in(CollUtil.isNotEmpty(area), HourTownEntity::getArea, area)
                .in(CollUtil.isNotEmpty(town), HourTownEntity::getTown, town)
                .groupBy(HourTownEntity::getArea, HourTownEntity::getTown)
                .orderByDesc(HourTownEntity::getDateMonth);

        return this.list(wrapper);
    }
}
