package com.bto.plant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.commons.pojo.dto.HourTownQuery;
import com.bto.commons.pojo.entity.HourTownEntity;

import java.util.List;

/**
 * 小时城镇服务接口
 * 
 * 提供小时城镇相关的业务服务，包括按时间范围查询城镇数据等功能
 *
 * <AUTHOR>
 * @since 2024/9/28 10:36
 */
public interface HourTownService extends IService<HourTownEntity> {

    /**
     * 查询小时城镇数据列表
     * 
     * 根据时间范围和查询条件获取小时城镇数据列表
     *
     * @param start 开始时间字符串
     * @param end 结束时间字符串
     * @param query 小时城镇查询条件对象
     * @return List<HourTownEntity> 小时城镇实体对象列表
     */
    List<HourTownEntity> list(String start, String end, HourTownQuery query);
}
