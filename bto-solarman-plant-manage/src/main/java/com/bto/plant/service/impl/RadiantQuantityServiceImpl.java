package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.pojo.entity.RadiantQuantityEntity;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.commons.utils.DateUtils;
import com.bto.plant.dao.RadiantQuantityDao;
import com.bto.commons.pojo.dto.RadiantQuantityQuery;
import com.bto.plant.service.RadiantQuantityService;
import com.bto.redis.utils.RedisUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 辐射量服务实现类
 *
 * 辐射量相关业务服务的具体实现类，提供辐射量数据查询、统计、预测等功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2023-12-29
 */
@Slf4j
@Service
@AllArgsConstructor
public class RadiantQuantityServiceImpl extends BaseServiceImpl<RadiantQuantityDao, RadiantQuantityEntity> implements RadiantQuantityService {

    private final RedisUtil redisUtil;
    private final SystemServiceClient systemServiceClient;
    private final RadiantQuantityDao radiantQuantityDao;

    /**
     * 获取指定天气站当日最新辐射量
     * 
     * @param weatherStationUid 天气站唯一标识
     * @return 当日最新辐射量数值（字符串格式）
     */ 
    @Override
    public String getRadiate(String weatherStationUid) {
        LambdaQueryWrapper<RadiantQuantityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RadiantQuantityEntity::getStationUid, weatherStationUid);
        String time = DateUtil.format(new Date(), "yyyy-MM-dd");
        wrapper.eq(RadiantQuantityEntity::getCollectDate, time);
        wrapper.orderByDesc(RadiantQuantityEntity::getUpdateTime);
        return baseMapper.selectOne(wrapper).getRadiantQuantity().toString();
    }

    /**
     * 根据条件查询辐射量总和
     * 
     * @param query 辐射量查询条件（包含天气站列表和时间范围）
     * @return 指定条件下辐射量的总和
     */ 
    @Override
    @DS("slave")
    public BigDecimal getRadiateByCondition(RadiantQuantityQuery query) {

        LambdaQueryWrapper<RadiantQuantityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(CollUtil.isNotEmpty(query.getWeatherStationUidList()), RadiantQuantityEntity::getStationUid, query.getWeatherStationUidList())
                .between(query.getStartTime() != null && query.getEndTime() != null, RadiantQuantityEntity::getCollectDate, query.getStartTime(), query.getEndTime())
                .select(RadiantQuantityEntity::getRadiantQuantity);
        return this.getBaseMapper().selectObjs(wrapper).stream()
                .map(obj -> (BigDecimal) obj)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

    }

    /**
     * 获取30天辐射量数据
     * <p>
     * 查询指定天气站在最近30天的辐射量数据，支持Redis缓存
     * </p>
     *
     * @param stationUidList 天气站ID列表
     * @param redisKey Redis缓存键
     * @return 30天辐射量数据列表
     */ 
    @DS("slave")
    @Override
    public List<Map<String, String>> getRadiateByThirtyDays(List<String> stationUidList, String redisKey) {

        Object obj = redisUtil.get(redisKey);
        if (obj != null) {
            return (List<Map<String, String>>) obj;
        }
        List<Map<String, String>> result = baseMapper.getRadiateByThirtyDays(stationUidList);
        redisUtil.set(redisKey, result, DateUtils.getExpTimeByEndOfToday());

        return result;
    }

    /**
     * 根据辐射量预测发电量
     * <p>
     * 基于给定的辐射量和项目ID，计算预测的发电量
     * 支持多项目汇总计算
     * </p>
     *
     * @param radiation 辐射量数值
     * @param projectId 项目ID
     * @return 预测发电量字符串
     */ 
    @Override
    public String getPredictionElectricityByRadiation(String radiation, String projectId) {
        List<String> projectList = systemServiceClient.getProjectIDListByPid(projectId).getData();
        if (CollUtil.isEmpty(projectList)) {
            return null;
        }
        BigDecimal result = BigDecimal.ZERO;
        for (String id : projectList) {
            BigDecimal prediction = new BigDecimal(Optional.ofNullable(radiantQuantityDao.getPredictionElectricityByRadiation(id, radiation)).orElse("0"));
            result = result.add(prediction);
        }
        return result.toString();

    }
}