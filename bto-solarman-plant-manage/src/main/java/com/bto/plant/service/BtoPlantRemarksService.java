package com.bto.plant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.BtoPlantRemarksDTO;
import com.bto.commons.pojo.entity.BtoPlantRemarksEntity;
import com.bto.commons.pojo.vo.BtoPlantRemarksVO;
import com.bto.commons.service.BaseService;

/**
 * 电站备注信息服务接口
 * 
 * 提供电站备注信息的管理服务，包括备注的增删改查、分页查询、清空等操作
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-06-20
 */
public interface BtoPlantRemarksService extends BaseService<BtoPlantRemarksEntity> {

    /**
     * 分页查询电站备注信息
     * 
     * 根据查询条件分页获取电站备注信息列表
     *
     * @param query 电站备注查询条件数据传输对象
     * @return IPage<BtoPlantRemarksEntity> 分页结果对象，包含电站备注实体信息
     */
    IPage<BtoPlantRemarksEntity> page(BtoPlantRemarksDTO query);

    /**
     * 保存电站备注信息
     * 
     * 保存新的电站备注信息到数据库
     *
     * @param vo 电站备注信息视图对象，包含需要保存的备注信息
     */
    void save(BtoPlantRemarksVO vo);

    /**
     * 更新电站备注信息
     * 
     * 更新已存在的电站备注信息
     *
     * @param vo 电站备注信息视图对象，包含需要更新的备注信息
     */
    void update(BtoPlantRemarksVO vo);

    /**
     * 清空电站备注信息
     * 
     * 根据电站唯一标识符清空该电站的所有备注信息
     *
     * @param plantUid 电站唯一标识符，用于指定需要清空备注的电站
     */
    void clear(String plantUid);
}