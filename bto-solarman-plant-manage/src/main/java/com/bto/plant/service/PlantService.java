package com.bto.plant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.pojo.dto.PlantQueryDTO;
import com.bto.commons.pojo.dto.PlantUpdateDTO;
import com.bto.commons.pojo.dto.PowerPlantInfoQueryDTO;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电站服务接口
 * 
 * 提供电站相关的核心业务服务，包括电站信息管理、查询、统计、收藏、注册等功能
 *
 * <AUTHOR>
 * @date 2023/3/30 17:34
 */
public interface PlantService {

    /**
     * 电站条件查询接口方法
     *
     * @param plantVO 电站查询条件
     * @param startPage 起始页码
     * @param pageSize 每页大小
     * @param sortType 排序类型
     * @param userInfo 用户信息
     * @return 电站分页数据
     */
    Page<com.bto.commons.pojo.dto.PlantVO> getPlants(com.bto.commons.pojo.dto.PlantVO plantVO, String startPage
            , String pageSize, String sortType, RequireParamsDTO userInfo);

    /**
     * 获取电站树
     *
     * @param plantName 电站名称
     * @param startPage 起始页码
     * @param pageSize  每页大小
     * @param userInfo 用户信息
     * @return 电站分页数据
     */
    Page<Plant> getPlantTree(String plantName, String startPage, String pageSize, RequireParamsDTO userInfo);

    /**
     * 查询电站详情
     *
     * @param plantUid 电站唯一标识符
     * @return 电站详情视图对象
     */
    com.bto.commons.pojo.dto.PlantVO getPlantInfo(String plantUid);

    /**
     * 通过电站Uid获取电站名称
     *
     * @param plantUid 电站唯一标识符
     * @return 电站名称
     */
    String getPlantNameByPlantUid(String plantUid);

    /**
     * 查询电站某月的发电数据
     *
     * @param plantUid 电站唯一标识符
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发电数据列表
     */
    List<HashMap<String, String>> getPlantElectricityStatsByMonth(String plantUid, String startTime, String endTime);

    /**
     * 查询电站某年的发电数据
     *
     * @param plantUid 电站唯一标识符
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发电数据列表
     */
    List<HashMap<String, BigDecimal>> getPlantElectricityStatsByYear(String plantUid, String startTime, String endTime);

    /**
     * 通过电站类型id查询电站类型名称--电站类型id和电站类型名称中间表
     *
     * @param plantTypeId 电站类型id
     * @return 电站类型名称
     */
    String getPlantTypeNameByPlantTypeId(String plantTypeId);

    /**
     * 更新电站详情
     *
     * @param plantUpdateDTO 电站更新信息数据传输对象
     * @return 更新影响的行数
     */
    Integer updatePlantInfo(PlantUpdateDTO plantUpdateDTO);


    /**
     * 通过plantUid获取电站信息
     *
     * @param plantUid 电站唯一标识符
     * @return 电站信息
     */
    Plant getPlantInfoByPlantUid(String plantUid);

    /**
     * 根据电站编号查询电站详情信息
     *
     * @param plantUid 电站编号
     * @return 电站详情信息
     */
    PlantVO getPlantDetail(String plantUid);

    /**
     * 获取电站列表
     *
     * @param query 查询条件
     * @param userInfo 用户信息
     * @return 电站列表
     */
    PlantListPageVO<PlantInfoVO> getPlantList(PlantQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 收藏电站接口
     *
     * @param userUid 用户唯一标识符
     * @param plantUid 电站唯一标识符
     * @return 影响行数
     */
    Integer collectPlant(String userUid, String plantUid);

    /**
     * 通过电站名称获取电站信息
     *
     * @param plantName 电站名称
     * @return 电站信息
     */
    Plant getPlantInfoByPlantName(String plantName);

    /**
     * 获取所有电站id和名称
     *
     * @param userInfo 用户信息
     * @param plantName 电站名称
     * @return 电站id和名称列表
     */
    List<PlantIdWithNameVO> selectAll(RequireParamsDTO userInfo, String plantName);

    /**
     * 获取电站发电量数据
     *
     * @param plantUidList 电站id列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param redisKey redis键
     * @return 发电量数据
     */
    List<Map<String, String>> getElectricityByDate(List<String> plantUidList, String startTime, String endTime, String redisKey);

    /**
     * 获取电站发电量数据
     *
     * @param plantUidList 电站id列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param redisKey redis键
     * @return 发电量数据
     */
    Map<String, String> getElectricityByMonth(List<String> plantUidList, String startTime, String endTime, String redisKey);

    /**
     * 通过城市获取电站id
     *
     * @param city 城市
     * @return 电站id
     */
    List<String> getPlantIdByCity(String city);

    /**
     * 通过城市获取电站发电量数据
     *
     * @param city 城市
     * @return 发电量数据
     */
    List<BatteryDivinerVO> getElectricityByCity(String city);

    /**
     * 获取站内电站分页数据
     *
     * @param userInfo 用户信息
     * @param plantQueryDTO 电站查询条件
     * @return 电站分页数据
     */
    IPage<PlantBaseInfoVO> getInStationPlantPage(RequireParamsDTO userInfo, PlantQueryDTO plantQueryDTO);

    /**
     * 根据地理位置获取电站信息（分页）
     *
     * @param query 查询参数
     * @return 电站信息分页数据
     */
    IPage<PowerPlantInfoVO> getPowerPlantInfoByLocation(PowerPlantInfoQueryDTO query);

    /**
     * 导出发电效率报表Excel
     *
     * @param query 查询参数
     * @param response 响应对象
     */
    void getPowerPlantInfoByLocationExcel(PowerPlantInfoQueryDTO query, HttpServletResponse response);

    /**
     * 获取电站发电量排行榜（分页）
     *
     * @param query 查询参数
     * @return 电站发电量排行榜分页数据
     */
    IPage<WorkEfficiencyVO> getPlantElectricityRank(PowerPlantInfoQueryDTO query);

    /**
     * 获取气象站内的电站名称列表
     *
     * @param deviceAddress 设备地址
     * @return 电站名称列表
     */
    List<String> getPlantNameListInWeatherStation(String deviceAddress);

    /**
     * 通过pid获取城市
     *
     * @param projectId 项目id
     * @return 城市列表
     */
    List<String> getCitiesByPid(String projectId);

    /**
     * 注册电站
     *
     * @param plant 电站
     * @return 电站
     */
    Plant registerPlant(Plant plant);

    /**
     * 工程师获取电站列表
     *
     * @param query 查询条件
     * @return 电站列表
     */
    PlantListPageVO<Plant> engineerGetPlantList(PlantQueryDTO query);
}
