package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.api.feign.engineer.EngineerServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.WeatherStationQuery;
import com.bto.commons.pojo.entity.EdgeServerOrderEntity;
import com.bto.commons.pojo.entity.WeatherEntity;
import com.bto.commons.pojo.entity.WeatherStationEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.StationLatestVO;
import com.bto.commons.pojo.vo.WeatherStationVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.plant.dao.WeatherMapper;
import com.bto.plant.dao.WeatherStationDao;
import com.bto.plant.service.WeatherStationService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 气象站服务实现类
 *
 * 气象站相关业务服务的具体实现类，提供气象站管理、城市天气数据获取、监测数据查询等功能。
 * <AUTHOR>
 * @since 1.0.0 2024-01-05
 */
@Service
@AllArgsConstructor
public class WeatherStationServiceImpl extends ServiceImpl<WeatherStationDao, WeatherStationEntity> implements WeatherStationService {

    private final WeatherStationDao weatherStationDao;
    private final EngineerServiceClient engineerServiceClient;
    private final WeatherMapper weatherMapper;

    /**
     * 分页获取气象站列表
     *
     * 根据查询条件分页获取气象站信息
     *
     * @param query 气象站查询条件对象
     * @return PageResult<String> 分页结果对象，包含气象站信息
     */
    @Override
    @DS("slave")
    public PageResult<String> getPageList(WeatherStationQuery query) {
        PageHelper.startPage(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<WeatherStationEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.groupBy(WeatherStationEntity::getCity);
        List<WeatherStationEntity> list = baseMapper.getPageList();
        PageInfo<WeatherStationEntity> page = new PageInfo<>(list);
        List<String> collect = list.stream().map(WeatherStationEntity::getCity).collect(Collectors.toList());
        return new PageResult<>(collect, page.getTotal());
    }

    /**
     * 根据城市获取气象站列表
     *
     * 根据城市名称查询该城市下的所有气象站信息
     *
     * @param city 城市名称
     * @return List<WeatherStationVO> 气象站视图对象列表
     */
    @Override
    public List<WeatherStationVO> getWeatherStationByCity(String city) {
        return weatherStationDao.getWeatherStationByCity(city);
    }

    /**
     * 获取所有城市列表
     *
     * 查询系统中配置的所有城市名称列表
     *
     * @return List<String> 城市名称列表
     */
    @Override
    @DS("slave")
    public List<String> getAllCity() {
        LambdaQueryWrapper<WeatherStationEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.groupBy(WeatherStationEntity::getCity);
        List<WeatherStationEntity> weatherStationEntities = baseMapper.selectList(wrapper);
        return weatherStationEntities.stream().map(WeatherStationEntity::getCity).collect(Collectors.toList());
    }

    /**
     * 根据城市获取气象站监测数据
     *
     * 根据城市列表和指定日期获取各城市气象站的最新监测数据
     *
     * @param allCity 城市名称列表
     * @param date 查询日期，格式为yyyy-MM-dd
     * @return List<StationLatestVO> 气象站最新监测数据列表
     */
    @Override
    public List<StationLatestVO> getStationLatestDataByCity(List<String> allCity, String date) {
        return baseMapper.getStationLatestDataByCity(allCity, date);
    }

    /**
     * 根据城市列表获取气象站UID列表
     *
     * 根据城市名称列表查询对应的气象站唯一标识符列表
     *
     * @param cities 城市名称列表
     * @return List<String> 气象站UID列表
     */
    @Override
    @DS("slave")
    public List<String> getStationUidListByCities(List<String> cities) {
        LambdaQueryWrapper<WeatherStationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(CollUtil.isNotEmpty(cities), WeatherStationEntity::getCity, cities)
                .select(WeatherStationEntity::getStationUid);

        return this.list(wrapper).stream()
                .map(WeatherStationEntity::getStationUid)
                .distinct()
                .collect(Collectors.toList());

    }

    /**
     * 根据城市获取区域列表
     *
     * 根据城市名称列表查询对应的区域信息
     *
     * @param cities 城市名称列表
     * @return List<String> 区域信息列表
     */
    @Override
    @DS("slave")
    public List<String> getAreaByCities(List<String> cities) {
        LambdaQueryWrapper<WeatherStationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(CollUtil.isNotEmpty(cities), WeatherStationEntity::getCity, cities)
                .select(WeatherStationEntity::getArea);

        return this.list(wrapper).stream()
                .map(WeatherStationEntity::getArea)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 创建气象站
     *
     * 为指定电站创建气象站记录
     *
     * @param plantName 电站名称
     * @param operatorId 操作员ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @DS("slave")
    public void create(String plantName, String operatorId) {
        try {
            baseMapper.create(plantName, operatorId);
        } catch (Exception e) {
            log.error(e.toString());
            throw new BusinessException("气象站创建失败", e.getMessage());
        }
    }

    /**
     * 分页查询气象站
     *
     * 根据查询条件分页查询气象站实体信息
     *
     * @param query 气象站查询条件对象
     * @return PageResult<WeatherStationEntity> 分页结果对象，包含气象站实体信息
     */
    @Override
    @DS("slave")
    public PageResult<WeatherStationEntity> page(WeatherStationQuery query) {
        Page<WeatherStationEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<WeatherStationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(WeatherStationEntity::getDeviceAddress, "0C")
                .like(StrUtil.isNotBlank(query.getStationUid()), WeatherStationEntity::getStationUid, query.getStationUid())
                .like(StrUtil.isNotBlank(query.getStationName()), WeatherStationEntity::getStationName, query.getStationName())
        ;

        page = this.page(page, wrapper);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 变更操作员
     *
     * 变更气象站的操作员信息
     *
     * @param stationUid 气象站唯一标识符
     * @param newOperatorId 新操作员ID
     * @param oldOperatorId 原操作员ID
     */
    @DS("slave")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeOperator(String stationUid, String newOperatorId, String oldOperatorId) {
        Result result = engineerServiceClient.getEdgeByOperatorSn(Arrays.asList(newOperatorId));
        if (!result.getStatus().equalsIgnoreCase(ResultEnum.SUCCESS.getCode())) {
            log.error("查询运维器失败:" + result.getMessage());
            // throw new BusinessException(ResultEnum.OPERATION_FAILED);
            throw new BusinessException("查询运维器失败:" + result.getMessage());
        }

        List<EdgeServerOrderEntity> operators = JSONUtil.toList(JSON.toJSONString(result.getData()), EdgeServerOrderEntity.class);
        if (CollUtil.isEmpty(operators)) {
            throw new BusinessException("运维器不存在");
        }
        EdgeServerOrderEntity serverOrderEntity = operators.get(0);

        this.lambdaUpdate()
                .eq(WeatherStationEntity::getStationUid, stationUid)
                .eq(WeatherStationEntity::getEdgServerSn, oldOperatorId)
                .set(StrUtil.isNotBlank(serverOrderEntity.getCommModuleImei()), WeatherStationEntity::getImei, serverOrderEntity.getCommModuleImei())
                .set(WeatherStationEntity::getEdgServerSn, newOperatorId)
                .update();
    }

    /**
     * 根据城市获取最新天气信息
     *
     * 根据城市名称获取该城市最新的天气信息
     *
     * @param city 城市名称
     * @return WeatherEntity 天气信息实体对象
     */
    @Override
    @DS("slave")
    public WeatherEntity getWeatherByPlantUid(String city) {
        return weatherMapper.getWeatherByPlantUid("bto_weather_" + DateUtil.format(new Date(), "yyyyMM"), city);
    }
}