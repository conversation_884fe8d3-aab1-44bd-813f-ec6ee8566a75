package com.bto.plant.service;

import com.bto.commons.pojo.dto.PlantAddressDTO;
import com.bto.commons.pojo.dto.PlantCarouselDTO;
import com.bto.commons.pojo.dto.RankWorkEfficiencyQueryDTO;
import com.bto.commons.pojo.vo.AreaCoordinateInfoVO;
import com.bto.commons.pojo.vo.PlantCoordinateInfoVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.WorkEfficiencyVO;

import java.util.List;

/**
 * 轮播服务接口
 * 
 * 提供轮播相关的业务服务，包括工作效率排行、站点轮播展示、
 *
 * <AUTHOR>
 * @date 2023/4/21 15:53
 */
public interface CarouselService {

    /**
     * 获取工作效率排行
     * 
     * 根据查询条件和用户信息获取工作效率排行数据
     *
     * @param query 工作效率排行查询条件数据传输对象
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @return List<WorkEfficiencyVO> 工作效率排行视图对象列表
     * <AUTHOR>
     * @since 2024-03-18 16:34:23
     */
    List<WorkEfficiencyVO> getRankWorkEfficiency(RankWorkEfficiencyQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 获取站点轮播数据
     * 
     * 根据起始索引和用户信息获取站点轮播展示数据
     *
     * @param startIndex 起始索引位置
     * @param endIndex 结束索引位置
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @return PlantCarouselDTO 站点轮播数据传输对象
     */
    PlantCarouselDTO getPlantCarousel(String startIndex, String endIndex, RequireParamsDTO userInfo);

    /**
     * 获取电站坐标信息
     * 
     * 根据电站地址信息和用户信息获取电站坐标数据
     *
     * @param plantAddressDTO 电站地址数据传输对象
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @return List<PlantCoordinateInfoVO> 电站坐标信息视图对象列表
     */
    List<PlantCoordinateInfoVO> getPlantCoordinate(PlantAddressDTO plantAddressDTO, RequireParamsDTO userInfo);

    /**
     * 获取各省市电站信息与坐标
     * 
     * 根据电站地址信息和用户信息获取各省市的电站信息及坐标数据
     *
     * @param plantAddressDTO 电站地址数据传输对象
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @return List<AreaCoordinateInfoVO> 区域坐标信息视图对象列表
     */
    List<AreaCoordinateInfoVO> getPlantInfoCoordinateOfArea(PlantAddressDTO plantAddressDTO, RequireParamsDTO userInfo);
}
