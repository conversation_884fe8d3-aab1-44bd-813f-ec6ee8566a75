package com.bto.plant.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.vo.CustomerContractVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.vo.CustomerContractInfoVO;
import com.bto.commons.pojo.dto.CustomerContractQueryDTO;

import java.util.List;

/**
 * 客户合同服务接口
 *
 * 提供客户合同相关的业务服务，包括合同信息查询、新增、批量插入、数据去重、合同编号管理等功能
 *
 * <AUTHOR>
 * @date 2023/8/8 15:16
 */
public interface CustomerContractService {

    /**
     * 获取客户合同信息
     *
     * 根据查询条件和用户信息分页获取客户合同信息列表
     *
     * @param query 客户合同查询条件数据传输对象
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @return IPage<CustomerContractVO> 分页结果对象，包含客户合同视图信息
     */
    IPage<CustomerContractVO> getCustomerContractInfo(CustomerContractQueryDTO query, RequireParamsDTO userInfo);

    /**
     * 新增客户合同信息
     *
     * 保存新的客户合同信息到数据库
     *
     * @param contractInfo 客户合同信息视图对象，包含需要保存的合同信息
     */
    void addCustomerContractInfo(CustomerContractInfoVO contractInfo);

    /**
     * 批量插入客户合同信息
     *
     * 批量保存客户合同信息到数据库，支持并发插入
     *
     * @param contractInfoList 客户合同信息视图对象列表，包含需要批量保存的合同信息
     * @return Integer 成功插入的记录数量
     * @throws IllegalAccessException 当访问权限异常时抛出
     * @throws InterruptedException 当线程被中断时抛出
     */
    Integer batchInsertCustomerContractInfo(List<CustomerContractInfoVO> contractInfoList) throws IllegalAccessException, InterruptedException;

    /**
     * 客户合同信息数据去重
     *
     * 对合同信息列表进行去重处理，确保数据的唯一性
     *
     * @param contractInfoList 客户合同信息视图对象列表，需要去重的合同数据
     * @return List<CustomerContractVO> 去重后的客户合同视图对象列表
     */
    List<CustomerContractVO> reduceContractList(List<CustomerContractVO> contractInfoList);

    /**
     * 新增博通合同编号
     *
     * 根据查询条件为合同新增博通合同编号
     *
     * @param query 客户合同查询条件数据传输对象
     */
    void addBtoContractNumber(CustomerContractQueryDTO query);
}
