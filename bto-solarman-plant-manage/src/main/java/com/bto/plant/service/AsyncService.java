package com.bto.plant.service;

import com.bto.commons.pojo.vo.CustomerContractVO;
import com.bto.plant.dao.CustomerContractMapper;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 异步服务接口
 * 提供异步执行相关操作的接口定义，主要用于处理批量合同信息等耗时操作
 *
 * <AUTHOR>
 * @date 2023/8/10 16:13
 */
public interface AsyncService {
    /**
     * 执行异步方法
     * 异步处理合同信息列表，使用CountDownLatch进行线程同步，通过AtomicReference回滚标志位处理异常情况
     *
     * @param contractInfo  合同信息列表，包含需要处理的客户合同数据
     * @param contractMapper 合同数据访问对象，用于数据库操作
     * @param countDownLatch 计数器，用于线程同步，确保所有异步任务完成
     * @param rollbackFlag  回滚标志位，原子引用类型，用于标记是否需要回滚操作
     */
    void executeAsync(List<CustomerContractVO> contractInfo,
                      CustomerContractMapper contractMapper,
                      CountDownLatch countDownLatch,
                      AtomicReference<Boolean> rollbackFlag
                      );
}
