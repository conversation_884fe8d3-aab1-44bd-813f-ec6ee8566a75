package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.handler.MapResultHandler;
import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.CounterDao;
import com.bto.plant.dao.FunctionalInstrumentDao;
import com.bto.plant.service.FunctionalInstrumentService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 多功能仪表数据服务实现类
 * 
 * 多功能仪表设备相关业务服务的具体实现类，提供多功能仪表设备的分页查询、设备关联等功能。
 *
 * <AUTHOR>
 * @since 2024-10-09
 */
@Service
@AllArgsConstructor
@Slf4j
public class FunctionalInstrumentServiceImpl extends BaseServiceImpl<FunctionalInstrumentDao, FunctionalInstrumentEntity> implements FunctionalInstrumentService {

    public final GlobalParamUtil globalParamUtil;
    public final DeviceServiceClient deviceServiceClient;
    public final CounterDao counterDao;

    /**
     * 分页查询多功能仪表信息
     * 
     * 根据查询条件获取多功能仪表设备的分页数据，包含与并网柜设备的关联信息。
     * 通过设备服务获取多功能仪表设备列表，并整合并网柜关联信息，提供完整的设备视图。
     * 
     * 数据处理流程：
     * - 通过设备管理服务获取指定电站的多功能仪表设备ID列表
     * - 验证设备列表的有效性，确保设备存在
     * - 执行分页查询获取多功能仪表详细信息
     * - 关联查询并网柜设备信息，建立设备关联关系
     * - 返回结构化的分页结果数据
     * 
     * @param query 查询条件（包含电站ID、分页参数等）
     * @return 多功能仪表信息分页结果
     * @throws BusinessException 当电站不存在多功能仪表时抛出异常
     */
    @Override
    public PageResult<FunctionalInstrumentEntity> page(FunctionalInstrumentQuery query) {
        Result<List<String>> result = deviceServiceClient.getListByType(query.getPlantUid(), DeviceType.FUNCTIONAL_INSTRUMENT.getCode());
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            List<String> ids = result.getData(new TypeReference<List<String>>() {
            });
            if (CollUtil.isEmpty(ids)) {
                throw new BusinessException("电站不存在多功能仪表");
            }
            query.setDeviceId(ids);
        }

        Page<FunctionalInstrumentEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);
        List<FunctionalInstrumentEntity> records = page.getRecords();
        List<String> functionIds = records.stream().map(FunctionalInstrumentEntity::getDeviceId).collect(Collectors.toList());
        MapResultHandler<String, String> map= new MapResultHandler<>();
        counterDao.getMap(functionIds,map);

        records.forEach(item->{
            item.setInverterId(map.get(item.getDeviceId()));
        });
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 
     * 根据查询条件构建MyBatis-Plus查询条件包装器，支持多功能仪表设备的多维度查询。
     *
     * @param query 查询条件，包含多功能仪表相关的筛选参数
     * @return 查询条件包装器，包含完整的查询条件和排序规则
     */
    private LambdaQueryWrapper<FunctionalInstrumentEntity> getWrapper(FunctionalInstrumentQuery query) {
        LambdaQueryWrapper<FunctionalInstrumentEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }


}