package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.converter.dto.PlantDTOMapper;
import com.bto.commons.converter.vo.PlantVOMapper;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.CustomerContractQueryDTO;
import com.bto.commons.pojo.dto.ExportElectricityMeterBillDTO;
import com.bto.commons.pojo.entity.MeterData;
import com.bto.commons.pojo.entity.MeterElectricityBill;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.dto.PlantExportFileDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.ExcelUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.PlantMapper;
import com.bto.plant.service.CustomerContractService;
import com.bto.plant.service.ExportFileService;
import com.bto.plant.service.PlantService;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导出文件服务实现类
 * 
 * 提供各类数据导出功能，包括客户合同信息、电表数据、电费账单、月度报表、电站列表等数据的Excel格式导出。
 * 支持多种导出模板和格式，使用EasyExcel进行高性能处理，提供灵活的查询条件和数据整合功能。
 * 
 * <AUTHOR>
 * @date 2023/6/13 16:35
 */
@Service
public class ExportFileServiceImpl implements ExportFileService {
    @Autowired
    private PlantMapper plantMapper;
    @Autowired
    private PlantVOMapper plantVOMapper;
    @Autowired
    private PlantService plantService;
    @Autowired
    private PlantDTOMapper plantDTOMapper;
    @Autowired
    private GlobalParamUtil globalParamUtil;

    @Autowired
    private CustomerContractService customerContractService;

    /**
     * 导出客户合同信息
     * 
     * 根据查询条件导出客户合同信息，支持分页查询和Excel格式导出。
     * 自动处理分页参数，导出所有符合条件的客户合同数据。
     * 
     * @param query 客户合同查询条件，包含分页、筛选等参数
     * @param response HTTP响应对象，用于设置导出文件信息和流
     */
    @Override
    public void customerContractService(CustomerContractQueryDTO query, HttpServletResponse response) {
        query.setPageSize(-1);
        query.setCurrentPage(-1);
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<CustomerContractVO> page = customerContractService.getCustomerContractInfo(query, userInfo);
        ExcelUtils.excelExport(CustomerContractVO.class, "CustomerContract", "CustomerContract", page.getRecords());
        // CreateExcelUtils.exportToExcel(page.getRecords(), CustomerContractVO.class, "CustomerContract", response);
    }

    /**
     * 获取电表数据
     *
     * 根据查询条件获取电表相关数据
     *
     * @param query 电表账单导出查询条件数据传输对象
     * @return List<MeterData> 电表数据实体对象列表
     */
    @Override
    public List<MeterData> getMeterData(ExportElectricityMeterBillDTO query) {
        query.setRegexpDate(buildRegexpDate(query.getStartTime(), query.getEndTime()));
        return plantMapper.getMeterData(query);
    }

    /**
     * 获取电费账单数据
     *
     * 根据查询条件获取电费账单相关信息
     *
     * @param query 电表账单导出查询条件数据传输对象
     * @return List<ElectricityBillingVO> 电费账单信息视图对象列表
     */
    @Override
    public List<ElectricityBillingVO> getElectricityMeterBill(ExportElectricityMeterBillDTO query) {
        return plantMapper.getElectricityMeterBill(query);
    }

    /**
     * 使用模板导出数据
     *
     * 根据查询条件和模板导出电费账单数据到HTTP响应
     *
     * @param query 电表账单导出查询条件数据传输对象
     * @param response HTTP响应对象，用于文件下载
     */
    @Override
    public void exportWithTemplate(ExportElectricityMeterBillDTO query, HttpServletResponse response) {
        try {
            // 判断参数中的开始时间和结束时间是否为跨月份，如果是则说明暂不支持
            String startTime = query.getStartTime();
            String endTime = query.getEndTime();
            if (isCrossMonth(startTime, endTime)) {
                throw new BusinessException("暂不支持跨月份导出");
            }
            String fileName = URLEncoder.encode("光伏发电月度报表", "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            // 读取模板
            InputStream templateInputStream = new ClassPathResource("templates/report_template.xlsx").getInputStream();
            // 构建填充数据
            MeterElectricityBill data = new MeterElectricityBill();
            data.setCalcTime(query.getStartTime() + "至" + query.getEndTime());
            data.setReportDate(DateUtil.now());
            data.setMonth(getSmartMonthRangeLabel(query.getStartTime(), query.getEndTime()));
            data.setMeterReader("博通光云平台");
            data.setPreparedBy("广东博通新能源科技有限公司");
            List<MeterData> meterDataList = getMeterData(query);
            BigDecimal totalPowerGeneration = new BigDecimal(0);
            BigDecimal totalMunicipalElectricity = new BigDecimal(0);
            BigDecimal internetPower = new BigDecimal(0);

            for (MeterData meterData : meterDataList) {
                String lastMonthReading = meterData.getLastMonthReading();
                String thisMonthReading = meterData.getThisMonthReading();
                if (lastMonthReading != null && thisMonthReading != null) {
                    // 本月减去上月再乘倍率
                    BigDecimal lastMonthReadingBigDecimal = new BigDecimal(lastMonthReading);
                    BigDecimal thisMonthReadingBigDecimal = new BigDecimal(thisMonthReading);
                    BigDecimal amount = thisMonthReadingBigDecimal.subtract(lastMonthReadingBigDecimal).multiply(new BigDecimal(meterData.getMultiplier()));
                    meterData.setAmount(amount.toString());
                }
                String reverseLastMonth = meterData.getReverseLastMonth();
                String reverseThisMonth = meterData.getReverseThisMonth();
                if (reverseLastMonth != null && reverseThisMonth != null) {
                    BigDecimal reverseLastMonthBigDecimal = new BigDecimal(reverseLastMonth);
                    BigDecimal reverseThisMonthBigDecimal = new BigDecimal(reverseThisMonth);
                    BigDecimal reverseAmount = reverseThisMonthBigDecimal.subtract(reverseLastMonthBigDecimal).multiply(new BigDecimal(meterData.getMultiplier()));
                    meterData.setReverseAmount(reverseAmount.toString());
                }

                if (meterData.getMeterType().equals("光伏电表")) {
                    data.setThisMonthGenerated(meterData.getAmount());
                    query.setPhotovoltaicMeterId(meterData.getMeterNumber());
                    // 累计发电量=当月读数*倍率
                    data.setTotalGenerated(new BigDecimal(meterData.getThisMonthReading()).multiply(new BigDecimal(meterData.getMultiplier())).toString());
                    totalPowerGeneration = new BigDecimal(meterData.getAmount());
                }

                if (meterData.getMeterType().equals("供电电表")) {
                    data.setThisMonthGridSent(meterData.getReverseAmount());
                    query.setPowerMeterId(meterData.getMeterNumber());
                    // 累计上网电量=当月上网电量*倍率
                    data.setTotalGridSent(new BigDecimal(meterData.getReverseThisMonth()).multiply(new BigDecimal(meterData.getMultiplier())).toString());
                    totalMunicipalElectricity = new BigDecimal(meterData.getAmount());
                    internetPower = new BigDecimal(meterData.getReverseAmount());
                }
            }
            // 自发自用=发电量-上网电量
            data.setThisMonthSelfUsed(new BigDecimal(data.getThisMonthGenerated()).subtract(new BigDecimal(data.getThisMonthGridSent())).toString());
            // 累计自发自用=累计发电量-累计上网电量
            data.setTotalSelfUsed(new BigDecimal(data.getTotalGenerated()).subtract(new BigDecimal(data.getTotalGridSent())).toString());
            // 总用电量=totalPowerGeneration+totalMunicipalElectricity-internetPower
            data.setThisMonthUsed(totalPowerGeneration.add(totalMunicipalElectricity).subtract(internetPower).toString());
            data.setPlantName(meterDataList.get(0).getPlantName());

            data.setMeterData(meterDataList);
            List<ElectricityBillingVO> list = getElectricityMeterBill(query);
            // 判断当前月份是否尖（7、8、9），如果不是需要合并尖峰
            mergeTipIntoPeak(list, startTime, endTime);
            // 获取合计值
            BigDecimal totalConsumptionKwh = list.stream().filter(vo -> "合计".equals(vo.getTimePeriod())).map(ElectricityBillingVO::getConsumptionKwh).findFirst().orElse(BigDecimal.ZERO);
            // 获取误差值
            BigDecimal errorValue = new BigDecimal(data.getThisMonthSelfUsed()).subtract(totalConsumptionKwh);
            data.setErrorValue(errorValue.toString());

            ExcelWriterBuilder builder = EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateInputStream)
                    .inMemory(true); // 开启内存写入

            ExcelWriter excelWriter = builder.build();
            WriteSheet writeSheet = EasyExcel.writerSheet("发电量月度报表").build();
            WriteSheet writeSheet2 = EasyExcel.writerSheet("电费缴付通知单").build();

            // 填充单个对象（非列表字段）
            excelWriter.fill(data, writeSheet);
            FillWrapper tableRows = new FillWrapper("date", data.getMeterData());
            // 填充列表字段，确保使用 FillWrapper
            excelWriter.fill(tableRows, FillConfig.builder().forceNewRow(true).build(), writeSheet);
            FillWrapper billingListWrapper = new FillWrapper("list", list);
            excelWriter.fill(billingListWrapper, FillConfig.builder().forceNewRow(true).build(), writeSheet2);

            // 获取 Workbook 和 Sheet
            Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();
            Sheet sheet = workbook.getSheetAt(0);

            // 合并单元格
            Sheet sheet2 = workbook.getSheet("电费缴付通知单");
            CellRangeAddress mergeRegion = new CellRangeAddress(2, 6, 5, 5); // F3:F7
            sheet2.addMergedRegion(mergeRegion);
            setBorderForMergedRegion(sheet2, mergeRegion, workbook);
            // 设置单元格值
            Row row = sheet2.getRow(2);
            if (row == null) row = sheet2.createRow(2);
            Cell cell = row.getCell(5);
            if (cell == null) cell = row.createCell(5);
            cell.setCellValue(list.get(4).getDiscountedAmountCny().toString());

            // 检查并移除冲突合并区域（以防模板里已有合并）
            for (int i = sheet.getNumMergedRegions() - 1; i >= 0; i--) {
                CellRangeAddress region = sheet.getMergedRegion(i);
                if (region.isInRange(10, 11) || region.isInRange(11, 11) || region.isInRange(11, 12)) {
                    sheet.removeMergedRegion(i);
                }
                if (region.isInRange(7, 14) || region.isInRange(8, 14) || region.isInRange(9, 14) || region.isInRange(10, 14) || region.isInRange(11, 14)) {
                    sheet.removeMergedRegion(i);
                }
            }

            // 添加合并区域
            CellRangeAddress r1 = new CellRangeAddress(10, 11, 11, 11);
            CellRangeAddress r2 = new CellRangeAddress(10, 11, 12, 12);
            CellRangeAddress r3 = new CellRangeAddress(10, 11, 13, 13);
            CellRangeAddress r4 = new CellRangeAddress(7, 11, 14, 14);
            sheet.addMergedRegion(r1);
            sheet.addMergedRegion(r2);
            sheet.addMergedRegion(r3);
            sheet.addMergedRegion(r4);

            // 设置边框
            setBorderForMergedRegion(sheet, r1, workbook);
            setBorderForMergedRegion(sheet, r2, workbook);
            setBorderForMergedRegion(sheet, r3, workbook);
            setBorderForMergedRegion(sheet, r4, workbook);
            excelWriter.finish();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 合并尖峰 billing
     * @param list 集合
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    public void mergeTipIntoPeak(List<ElectricityBillingVO> list, String startTime, String endTime) {

        // 7-9 月无需合并
        if (isInSummerQuarter(startTime, endTime)) return;

        // ① 用时段做 key，方便 O(1) 访问
        Map<String, ElectricityBillingVO> byPeriod = list.stream()
                .collect(Collectors.toMap(ElectricityBillingVO::getTimePeriod, Function.identity()));

        ElectricityBillingVO tip  = byPeriod.get("尖");
        ElectricityBillingVO peak = byPeriod.get("峰");

        if (tip != null && peak != null) {
            /* 折扣率 = (优惠后金额 / 原金额)，保留 10 位小数避免精度损失 */
            BigDecimal discountRate = Optional.ofNullable(peak.getAmountCny())
                    .filter(a -> a.signum() != 0)
                    .map(a -> peak.getDiscountedAmountCny()
                            .divide(a, 10, RoundingMode.HALF_UP))
                    .orElse(BigDecimal.ZERO);

            // ② 合并数据到“峰”
            BigDecimal mergedKwh = peak.getConsumptionKwh().add(tip.getConsumptionKwh());
            peak.setConsumptionKwh(mergedKwh);
            peak.setAmountCny(mergedKwh.multiply(peak.getUnitPriceCnyPerKwh()).setScale(2, RoundingMode.HALF_UP));
            peak.setDiscountedAmountCny(peak.getAmountCny().multiply(discountRate).setScale(2, RoundingMode.HALF_UP));

            // ③ “尖”清零
            tip.setConsumptionKwh(BigDecimal.ZERO);
            tip.setAmountCny(BigDecimal.ZERO);
            tip.setDiscountedAmountCny(BigDecimal.ZERO);
        }

        // ④ 重算“合计”行
        ElectricityBillingVO total = byPeriod.get("合计");
        if (total != null) {
            total.setConsumptionKwh(sum(list, ElectricityBillingVO::getConsumptionKwh));
            total.setAmountCny(sum(list, ElectricityBillingVO::getAmountCny));
            total.setDiscountedAmountCny(
                    sum(list, ElectricityBillingVO::getDiscountedAmountCny)
                            .setScale(2, RoundingMode.HALF_UP));   // 两位小数四舍五入
        }
    }

    /**
     * 求和工具：排除“合计”行，防止自包含
     */
    private static BigDecimal sum(List<ElectricityBillingVO> list,
                                  Function<ElectricityBillingVO, BigDecimal> getter) {
        return list.stream()
                .filter(vo -> !"合计".equals(vo.getTimePeriod()))
                .map(getter)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 判断开始时间和结束时间是否跨月
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否跨月
     */
    public boolean isCrossMonth(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(startTime, formatter);
        LocalDate end = LocalDate.parse(endTime, formatter);
        // 判断是否跨月份
        return !(start.getYear() == end.getYear() && start.getMonth() == end.getMonth());
    }

    // 判断开始时间结束时间是否在尖月（7、8、9）
    public boolean isInSummerQuarter(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(startTime, formatter);
        LocalDate end = LocalDate.parse(endTime, formatter);
        return start.getMonthValue() >= 7 && start.getMonthValue() <= 9
                && end.getMonthValue() >= 7 && end.getMonthValue() <= 9;
    }

    /**
     * 获取智能月账单区间标签
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 区间标签
     */
    public String getSmartMonthRangeLabel(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime, formatter);
        LocalDate endDate = LocalDate.parse(endTime, formatter);

        int startYear = startDate.getYear();
        int endYear = endDate.getYear();
        int startMonth = startDate.getMonthValue();
        int endMonth = endDate.getMonthValue();

        if (startYear == endYear) {
            if (startMonth == endMonth) {
                return String.format("%02d月", startMonth);
            } else {
                return String.format("%02d-%02d月", startMonth, endMonth);
            }
        } else {
            return String.format("%d-%02d月-%d-%02d月", startYear, startMonth, endYear, endMonth);
        }
    }

    /**
     * 合并单元格
     *
     * @param sheet  sheet
     * @param region 合并区域
     * @param workbook workbook
     */
    private void setBorderForMergedRegion(Sheet sheet, CellRangeAddress region, Workbook workbook) {
        CellStyle borderStyle = workbook.createCellStyle();
        // 设置黑色边框
        borderStyle.setBorderTop(BorderStyle.THIN);
        borderStyle.setBorderBottom(BorderStyle.THIN);
        borderStyle.setBorderLeft(BorderStyle.THIN);
        borderStyle.setBorderRight(BorderStyle.THIN);
        borderStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        borderStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        borderStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        borderStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 设置居中对齐
        borderStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        // 应用样式到合并区域每个单元格
        for (int row = region.getFirstRow(); row <= region.getLastRow(); row++) {
            Row sheetRow = sheet.getRow(row);
            if (sheetRow == null) {
                sheetRow = sheet.createRow(row);
            }
            for (int col = region.getFirstColumn(); col <= region.getLastColumn(); col++) {
                Cell cell = sheetRow.getCell(col);
                if (cell == null) {
                    cell = sheetRow.createCell(col);
                }
                cell.setCellStyle(borderStyle);
            }
        }
    }


    /**
     * 构建用于数据库查询的正则表达式日期格式
     * 
     * 根据开始时间和结束时间构建MySQL正则表达式，用于精确匹配这两个日期
     * 主要用于电表数据查询，确保只查询指定开始和结束日期的数据
     * 
     * @param startTime 开始时间，格式必须为 yyyy-MM-dd
     * @param endTime 结束时间，格式必须为 yyyy-MM-dd
     * @return 构建好的MySQL正则表达式字符串，格式为 ^(startTime|endTime)
     * @throws IllegalArgumentException 当日期格式不合法时抛出异常
     */
    public String buildRegexpDate(String startTime, String endTime) {
        // 验证开始时间和结束时间的格式是否正确
        if (!isValidDate(startTime) || !isValidDate(endTime)) {
            throw new IllegalArgumentException("日期格式非法，必须为 yyyy-MM-dd");
        }
        // 构建正则表达式，^表示以开始，|表示或的关系
        return "^(" + startTime + "|" + endTime + ")";
    }

    /**
     * 判断日期格式是否正确
     *
     * @param dateStr 日期字符串
     * @return true-正确，false-错误
     */
    public boolean isValidDate(String dateStr) {
        try {
            LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }


    /**
     * 获取导出的电站列表
     *
     * @param userInfo 用户信息
     * @param query    查询参数
     * @return 电站列表
     */
    public List<PlantDigestVO> exportPlantList(RequireParamsDTO userInfo, PlantExportFileDTO query) throws IllegalAccessException {
        if (!StringUtils.isNotBlank(query.getSheetName()) && !StringUtils.isNotEmpty(query.getSheetName())) {
            query.setSheetName("电站列表");
        }
        QueryWrapper<Plant> plantQueryWrapper = new QueryWrapper<>();
        plantQueryWrapper.eq("is_deleted", "0");
        if (userInfo.getPlantList() != null && userInfo.getPlantList().size() > 0) {
            plantQueryWrapper.in("plant_uid", userInfo.getPlantList());
            plantQueryWrapper.like(StringUtils.isNotBlank(query.getPlantName()), "plant_name", query.getPlantName());
            plantQueryWrapper.in(!Collections.isEmpty(query.getMultiPlantStatus()), "plant_status", query.getMultiPlantStatus());
            plantQueryWrapper.eq(StringUtils.isNotBlank(query.getPowerDistributor()), "power_distributor", query.getPowerDistributor());
        } else if (userInfo.getProjectList() != null && userInfo.getProjectList().size() > 0) {
            plantQueryWrapper.in("project_special", userInfo.getProjectList());
            plantQueryWrapper.like(StringUtils.isNotBlank(query.getPlantName()), "plant_name", query.getPlantName());
            plantQueryWrapper.in(CollectionUtil.isNotEmpty(query.getMultiPlantStatus()), "plant_status", query.getMultiPlantStatus());
            plantQueryWrapper.eq(StringUtils.isNotBlank(query.getPowerDistributor()), "power_distributor", query.getPowerDistributor());
        } else {
            throw new BusinessException(ResultEnum.USER_ACCOUNT_INVALIDATED);
        }
        plantQueryWrapper.orderByAsc("plant_name");
        List<Plant> plantList = plantMapper.selectList(plantQueryWrapper);
        List<PlantDigestVO> plantDigestVOList = new ArrayList<PlantDigestVO>();
        List<ProjectBaseVO> allProjectList = globalParamUtil.getAllProjectList();
        for (Plant plant : plantList) {
            PlantDigestVO plantDigestVO = plantDTOMapper.plant2PlantDTODecorator(plant);
            plantDigestVOList.add(plantDigestVO);
            String projectName = allProjectList.stream().filter(project -> project.getId().equals(String.valueOf(plantDigestVO.getProjectSpecial()))).findFirst().map(ProjectBaseVO::getProjectName).orElse(null);
            plantDigestVO.setProjectName(projectName);
        }
        return plantDigestVOList;
    }
}
