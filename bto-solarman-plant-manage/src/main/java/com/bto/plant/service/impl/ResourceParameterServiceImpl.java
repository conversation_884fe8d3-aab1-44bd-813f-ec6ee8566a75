package com.bto.plant.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.commons.pojo.entity.ResourcesParameter;
import com.bto.plant.dao.ResourceParameterMapper;
import com.bto.plant.service.ResourceParameterService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 资源参数服务实现类
 * 
 * 资源参数相关业务服务的具体实现类，提供城市级资源参数的查询功能。
 * 
 * <AUTHOR>
 * @since 2024/1/7 14:47
 */
@Service
@AllArgsConstructor
public class ResourceParameterServiceImpl extends ServiceImpl<ResourceParameterMapper, ResourcesParameter> implements ResourceParameterService {
    /**
     * 根据城市获取资源参数信息
     * 
     * 根据城市名称查询该城市的资源参数信息，使用从库数据源进行查询
     *
     * @param city 城市名称
     * @return 资源参数实体对象
     */
    @Override
    @DS("slave")
    public ResourcesParameter getInfoByCity(String city) {
        LambdaQueryWrapper<ResourcesParameter> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(city), ResourcesParameter::getCity,city);
        return this.getOne(wrapper);
    }
}
