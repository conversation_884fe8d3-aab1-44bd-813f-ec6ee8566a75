package com.bto.plant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.commons.pojo.dto.SensorQuery;
import com.bto.commons.pojo.entity.SensorEntity;
import com.bto.commons.pojo.vo.PageResult;

import java.util.Date;
import java.util.List;

/**
 * 传感器服务接口
 * 
 * 提供传感器相关的业务服务，包括传感器数据分页查询、详情获取等功能
 *
 * <AUTHOR>
 * @since 2024/10/7 16:26
 */
public interface SensorService extends IService<SensorEntity> {
    /**
     * 分页查询传感器数据
     * 
     * 根据查询条件分页获取传感器信息列表
     *
     * @param query 传感器查询条件对象，包含分页和过滤参数
     * @return PageResult<SensorEntity> 分页结果对象，包含传感器实体信息
     */
    PageResult<SensorEntity> page(SensorQuery query);

    /**
     * 获取传感器详情
     * 
     * 根据传感器ID和日期获取传感器的详细数据
     *
     * @param sensorId 传感器唯一标识符
     * @param date 查询日期
     * @return List<SensorEntity> 传感器实体对象列表
     */
    List<SensorEntity> detail(String sensorId, Date date);
}
