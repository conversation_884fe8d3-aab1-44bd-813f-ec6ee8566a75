package com.bto.plant.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.pojo.dto.BtoDeviceHomeQuery;
import com.bto.commons.pojo.entity.BtoDeviceHomeEntity;
import com.bto.commons.pojo.vo.BtoDeviceHomeVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.oauth.global.GlobalParamUtil;
import lombok.AllArgsConstructor;
import com.bto.plant.convert.BtoDeviceHomeConvert;
import com.bto.plant.dao.BtoDeviceHomeDao;
import com.bto.plant.service.BtoDeviceHomeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 家居设备服务实现类
 * 
 * 家居设备相关业务服务的具体实现类，提供家居设备信息管理、查询、增删改查等功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
@Service
@AllArgsConstructor
public class BtoDeviceHomeServiceImpl extends BaseServiceImpl<BtoDeviceHomeDao, BtoDeviceHomeEntity> implements BtoDeviceHomeService {

    private final GlobalParamUtil globalParamUtil;

    /**
     * 获取家居设备列表
     *
     * 根据查询条件获取家居设备信息列表
     *
     * @param query 设备查询条件对象，包含过滤和分页参数
     * @return List<BtoDeviceHomeVO> 设备信息视图对象列表
     */
    @Override
    public List<BtoDeviceHomeVO> getList(BtoDeviceHomeQuery query) {
        globalParamUtil.checkUserPermission(query.getUserUid());
        List<BtoDeviceHomeEntity> list = baseMapper.selectList(getWrapper(query));
        return BtoDeviceHomeConvert.INSTANCE.convertList(list);
    }

    /**
     * 构建查询条件包装器
     * 
     * 根据查询条件构建MyBatis-Plus查询条件包装器，支持家居设备的多维度查询。
     * 支持按用户ID、设备地址等条件进行精确查询，并按排序和更新时间降序排列。
     * 
     * @param query 查询条件，包含用户ID、设备地址等筛选条件
     * @return 查询条件包装器，包含完整的查询条件和排序规则
     */
    private LambdaQueryWrapper<BtoDeviceHomeEntity> getWrapper(BtoDeviceHomeQuery query) {
        LambdaQueryWrapper<BtoDeviceHomeEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BtoDeviceHomeEntity::getUserUid, query.getUserUid());
        wrapper.eq(Objects.nonNull(query.getDeviceAddr()), BtoDeviceHomeEntity::getDeviceAddr, query.getDeviceAddr());
        wrapper.orderByDesc(BtoDeviceHomeEntity::getOrder);
        wrapper.orderByDesc(BtoDeviceHomeEntity::getUpdateTime);
        return wrapper;
    }

    /**
     * 处理保存或更新操作
     *
     * 根据设备信息视图对象保存新设备或更新现有设备信息
     *
     * @param vo 设备信息视图对象，包含设备的所有相关信息
     */
    @Override
    public void handleSaveOrUpdate(BtoDeviceHomeVO vo) {
        globalParamUtil.checkUserPermission(vo.getUserUid());
        BtoDeviceHomeEntity entity = BtoDeviceHomeConvert.INSTANCE.convert(vo);
        if (entity.getDeviceId() == null) {
            // todo 设置设备id
            baseMapper.insert(entity);
        } else {
            updateById(entity);
        }
    }

    /**
     * 删除设备
     *
     * 根据查询条件删除指定的家居设备
     *
     * @param query 设备查询条件对象，指定要删除的设备
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(BtoDeviceHomeQuery query) {
        globalParamUtil.checkUserPermission(query.getUserUid());
        removeByIds(query.getDeviceIds());
    }

    /**
     * 获取设备详细信息
     *
     * 根据设备ID和用户UID获取指定设备的详细信息
     *
     * @param deviceId 设备唯一标识符
     * @param userUid 用户唯一标识符，用于权限验证
     * @return BtoDeviceHomeVO 设备详细信息视图对象
     */
    @Override
    public BtoDeviceHomeVO getDeviceInfo(String deviceId, String userUid) {
        globalParamUtil.checkUserPermission(userUid);
        LambdaQueryWrapper<BtoDeviceHomeEntity> queryWrapper = Wrappers.lambdaQuery(BtoDeviceHomeEntity.class);
        queryWrapper.eq(BtoDeviceHomeEntity::getDeviceId, deviceId);
        queryWrapper.eq(BtoDeviceHomeEntity::getUserUid, userUid);
        BtoDeviceHomeEntity btoDeviceHomeEntity = baseMapper.selectOne(queryWrapper);
        if (btoDeviceHomeEntity != null) {
            return BtoDeviceHomeConvert.INSTANCE.convert(btoDeviceHomeEntity);
        }
        return null;
    }

}