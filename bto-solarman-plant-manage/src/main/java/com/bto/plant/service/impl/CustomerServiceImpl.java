package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.bto.commons.pojo.entity.WeatherStationEntity;
import com.bto.commons.pojo.vo.DateRadiationVO;
import com.bto.commons.pojo.vo.StationLatestVO;
import com.bto.commons.pojo.vo.WeatherFutureVO;
import com.bto.plant.dao.WeatherStationDao;
import com.bto.plant.service.CustomerService;
import com.bto.plant.service.ForecastService;
import com.bto.plant.service.WeatherStationService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户服务实现类
 * 
 * 客户相关业务服务的具体实现类，提供定制化气象站数据查询功能。
 * 主要服务于特定客户（如新华丽）的电站数据展示和分析场景。
 * 
 * <AUTHOR>
 * @since 2024/9/21 17:49
 */
@Service
@AllArgsConstructor

public class CustomerServiceImpl implements CustomerService {
    private final ForecastService forecastService;
    private final WeatherStationService weatherStationService;
    private final WeatherStationDao weatherStationDao;

    /**
     * 获取定制化气象站数据
     * 
     * 为特定客户（新华丽）提供定制化的气象站数据查询服务，
     * 包含气象站信息、辐射数据、未来天气预报等综合数据。
     * 
     * 数据组成：
     * - 新华丽专属气象站站点信息
     * - 各城市的辐射数据和趋势分析
     * - 未来天气预报和气象趋势
     * - 按城市维度组织的数据集合
     * 
     * @return 定制化气象站数据集合（包含站点数据、辐射数据、天气数据）
     */ 
    @DS("slave")
    @Override
    public HashMap<String, Object> getCustomStation() {
        List<WeatherStationEntity> list = weatherStationService.lambdaQuery()
                .like(WeatherStationEntity::getStationName, "新华丽")
                .groupBy(WeatherStationEntity::getStationUid)
                .list();
        HashMap<String, Object> map = new HashMap<>();
        List<String> idList = list.stream().map(WeatherStationEntity::getStationUid).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)) {
            return map;
        }

        List<StationLatestVO> customerStation = weatherStationDao.getCustomStation(idList);
        List<String> cities = customerStation.stream().map(StationLatestVO::getCity).distinct().collect(Collectors.toList());
        for (String city : cities) {
                HashMap<String, List<DateRadiationVO>> radiationData = forecastService.getDateRadiationByCity(city);
                HashMap<String, List<WeatherFutureVO>> weatherFutureData = forecastService.getWeatherByCity(city);
                map.put("stationData", customerStation);
                map.put("radiationData", radiationData);
                map.put("weatherFutureData", weatherFutureData);
        }
        return map;
    }
}
