package com.bto.plant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.BtoElectricityMeter;
import com.bto.commons.pojo.entity.CounterEntity;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.CounterDao;
import com.bto.plant.service.CounterService;
import com.bto.plant.service.FunctionalInstrumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 并网柜服务实现类
 * 
 * 并网柜相关业务服务的具体实现类，提供并网柜设备的分页查询、设备关联等功能。
 * 支持与电表、功能仪器的设备关联和数据整合，为电站配电系统提供完整的管理解决方案。
 * 
 * <AUTHOR>
 * @since 2024/10/7 15:52
 */
@Service
@RequiredArgsConstructor
public class CounterServiceImpl extends ServiceImpl<CounterDao, CounterEntity> implements CounterService {

    public final GlobalParamUtil globalParamUtil;
    public final DeviceServiceClient deviceServiceClient;
    public final FunctionalInstrumentService functionalInstrumentService;


    /**
     * 分页查询并网柜信息
     * 
     * 根据查询条件获取并网柜设备的分页数据，包含关联的电表和功能仪器信息。
     * 通过设备服务获取并网柜设备列表，并整合相关设备数据，提供完整的设备关联视图。
     *
     * @param query 查询条件（包含电站ID、分页参数等）
     * @return 并网柜信息分页结果，包含关联的电表和功能仪器信息
     * @throws BusinessException 当电站不存在并网柜时抛出异常
     */ 
    @Override
    public PageResult<CounterVO> page(CounterQuery query) {
        Result<List<String>> result = deviceServiceClient.getListByType(query.getPlantUid(), DeviceType.CABINET_COUNTER.getCode());
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            List<String> ids = result.getData(new TypeReference<List<String>>() {
            });
            if (CollUtil.isEmpty(ids)) {
                throw new BusinessException("电站不存在并网柜");
            }
            query.setCabinetId(ids);
        }

        Page<CounterEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        //获取并网柜
        page = baseMapper.getPage(page, query, null);
        List<CounterVO> vos = BeanUtil.copyToList(page.getRecords(), CounterVO.class);

        //获取电表
        Result<List<BtoElectricityMeter>> meterResponse = deviceServiceClient.getInfoById(query.getPlantUid());
        List<BtoElectricityMeter> meterData = new ArrayList<>();
        if (meterResponse.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            meterData =meterResponse.getData();
        }

        //获取电表
        FunctionalInstrumentQuery functionalInstrumentQuery = new FunctionalInstrumentQuery();
        functionalInstrumentQuery.setPlantUid(query.getPlantUid());
        functionalInstrumentQuery.setPageSize(-1);
        functionalInstrumentQuery.setCurrentPage(-1);
        List<FunctionalInstrumentEntity> functionData = functionalInstrumentService.page(functionalInstrumentQuery).getList();

        // List<String> cabinetIds = vos.stream().map(CounterVO::getCabinetId).collect(Collectors.toList());
        // List<String> childrenIds = baseMapper.getMeterIdsById(cabinetIds);
        for (CounterVO vo : vos) {
            List<String> deviceIds = baseMapper.getDeviceIdsById(Collections.singletonList(vo.getCabinetId()));

            if (CollUtil.isNotEmpty(meterData)){
                ArrayList<BtoElectricityMeter> meters = new ArrayList<>();
                for (BtoElectricityMeter meter : meterData) {
                    if (deviceIds.contains(meter.getVoltmeterId())){
                        meters.add(meter);
                    }
                }
                vo.setMeters(meters);
            }

            if (CollUtil.isNotEmpty(functionData)){
                ArrayList<FunctionalInstrumentEntity> functions = new ArrayList<>();
                for (FunctionalInstrumentEntity function : functionData) {
                    if (deviceIds.contains(function.getDeviceId())){
                        functions.add(function);
                    }
                }
                vo.setFunctionalInstruments(functions);
            }
        }
        return new PageResult<>(vos, page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 
     * 根据查询条件构建MyBatis-Plus查询条件包装器，支持并网柜设备的多维度查询。
     * 支持按并网柜ID、设备状态等条件进行模糊查询，提供灵活的筛选功能。
     * 
     * @param query 查询条件，包含并网柜ID、设备状态等筛选参数
     * @return 查询条件包装器，包含完整的查询条件
     */ 
    private static LambdaQueryWrapper<CounterEntity> getWrapper(CounterQuery query) {
        LambdaQueryWrapper<CounterEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .like(query.getCabinetId() != null, CounterEntity::getCabinetId, query.getCabinetId());
        return wrapper;
    }
}
