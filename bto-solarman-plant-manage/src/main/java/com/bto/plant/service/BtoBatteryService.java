package com.bto.plant.service;

import com.bto.commons.pojo.dto.BatteryQuery;
import com.bto.commons.pojo.entity.BtoBattery;
import com.bto.commons.pojo.vo.BatteryVO;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import com.bto.commons.pojo.vo.EnergySocAnalyzeVO;
import com.bto.commons.service.BaseService;

import java.util.List;

/**
 * 电池服务接口
 * 
 * 提供电池相关的业务服务，包括SOC分析、图表数据获取、
 * 能量分析、电池保护信息等功能
 *
 * <AUTHOR> by zhb on 2025/2/12.
 */
public interface BtoBatteryService extends BaseService<BtoBattery> {

    /**
     * 获取SOC分析数据
     * 
     * 根据逆变器序列号和日期时间获取电池的SOC（荷电状态）分析数据
     *
     * @param inverterSn 逆变器序列号，用于标识特定逆变器设备
     * @param dateTime 日期时间字符串，格式为yyyy-MM-dd HH:mm:ss
     * @return List<EnergySocAnalyzeVO> 能量SOC分析视图对象列表
     */
    List<EnergySocAnalyzeVO> getSocAnalyze(String inverterSn, String dateTime);

    /**
     * 获取图表数据
     * 
     * 根据电池查询条件获取电池图表展示所需的数据
     *
     * @param query 电池查询条件对象，包含查询参数和过滤条件
     * @return List<BatteryVO> 电池视图对象列表，用于图表展示
     */
    List<BatteryVO> chart(BatteryQuery query);

    /**
     * 能量分析
     * 
     * 根据电池查询条件进行电池能量分析，获取分析结果数据
     *
     * @param query 电池查询条件对象，包含查询参数和过滤条件
     * @return List<BatteryVO> 电池视图对象列表，包含分析结果
     */
    List<BatteryVO> energyAnalysis(BatteryQuery query);

    /**
     * 获取电池保护信息
     * 
     * 根据电池查询条件获取电池保护相关的信息
     *
     * @param query 电池查询条件对象，包含查询参数和过滤条件
     * @return List<BtoAcFireInfoVO> 空调消防信息视图对象列表
     */
    List<BtoAcFireInfoVO> getBatteryGuardInfo(BatteryQuery query);
}
