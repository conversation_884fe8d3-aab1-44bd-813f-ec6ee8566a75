package com.bto.plant.service;

import com.bto.commons.pojo.entity.BtoBatteryInfo;
import com.bto.commons.pojo.vo.BatteryInfoVO;
import com.bto.commons.service.BaseService;

/**
 * 电池信息服务接口
 * 
 * 提供电池集群相关的信息查询服务，包括电池簇信息获取等功能
 *
 * <AUTHOR> by zhb on 2025/4/24.
 */
public interface BtoBatteryInfoService extends BaseService<BtoBatteryInfo> {

    /**
     * 获取电池簇信息
     * 
     * 根据逆变器序列号获取对应的电池簇详细信息
     *
     * @param inverterSn 逆变器序列号，用于标识特定逆变器设备
     * @return BatteryInfoVO 电池信息视图对象，包含电池簇的详细信息
     */
    BatteryInfoVO getBatteryClusterInfo(String inverterSn);
}