package com.bto.plant.service;

import com.bto.commons.pojo.dto.CustomerContractQueryDTO;
import com.bto.commons.pojo.dto.ExportElectricityMeterBillDTO;
import com.bto.commons.pojo.entity.MeterData;
import com.bto.commons.pojo.vo.ElectricityBillingVO;
import com.bto.commons.pojo.vo.PlantDigestVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.pojo.dto.PlantExportFileDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件导出服务接口
 * 
 * 提供文件导出的相关业务服务，包括电站列表导出、客户合同导出、电表数据导出、电费账单导出等功能
 *
 * <AUTHOR>
 * @date 2023/6/13 16:35
 */
public interface ExportFileService {

    /**
     * 导出电站列表
     * 
     * 根据用户信息和查询条件导出电站列表数据
     *
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @param query 电站导出查询条件数据传输对象
     * @return List<PlantDigestVO> 电站摘要信息视图对象列表
     * @throws IllegalAccessException 当访问权限异常时抛出
     */
    List<PlantDigestVO> exportPlantList(RequireParamsDTO userInfo, PlantExportFileDTO query) throws IllegalAccessException;

    /**
     * 导出客户合同信息
     * 
     * 根据查询条件导出客户合同信息到HTTP响应
     *
     * @param query 客户合同查询条件数据传输对象
     * @param response HTTP响应对象，用于文件下载
     */
    void customerContractService(CustomerContractQueryDTO query, HttpServletResponse response);

    /**
     * 获取电表数据
     * 
     * 根据查询条件获取电表相关数据
     *
     * @param query 电表账单导出查询条件数据传输对象
     * @return List<MeterData> 电表数据实体对象列表
     */
    List<MeterData> getMeterData(ExportElectricityMeterBillDTO query);

    /**
     * 获取电费账单数据
     * 
     * 根据查询条件获取电费账单相关信息
     *
     * @param query 电表账单导出查询条件数据传输对象
     * @return List<ElectricityBillingVO> 电费账单信息视图对象列表
     */
    List<ElectricityBillingVO> getElectricityMeterBill(ExportElectricityMeterBillDTO query);

    /**
     * 使用模板导出数据
     * 
     * 根据查询条件和模板导出电费账单数据到HTTP响应
     *
     * @param query 电表账单导出查询条件数据传输对象
     * @param response HTTP响应对象，用于文件下载
     */
    void exportWithTemplate(ExportElectricityMeterBillDTO query, HttpServletResponse response);
}
