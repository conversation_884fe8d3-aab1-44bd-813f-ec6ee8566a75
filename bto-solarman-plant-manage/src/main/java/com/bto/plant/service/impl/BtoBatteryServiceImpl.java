package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.BatteryQuery;
import com.bto.commons.pojo.entity.BtoBattery;
import com.bto.commons.pojo.vo.BatteryVO;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import com.bto.commons.pojo.vo.EnergySocAnalyzeVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.commons.utils.TableUtils;
import com.bto.plant.dao.BtoBatteryDao;
import com.bto.plant.service.BtoAcFireInfoService;
import com.bto.plant.service.BtoBatteryService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * BTO电池服务实现类
 * 提供电池SOC分析、图表展示、能量分析等功能
 * 
 * <AUTHOR> by zhb on 2025/2/12.
 */
@Service
@AllArgsConstructor
public class BtoBatteryServiceImpl extends BaseServiceImpl<BtoBatteryDao, BtoBattery> implements BtoBatteryService {

    private final BtoBatteryDao btoBatteryDao;
    private final DeviceServiceClient deviceServiceClient;
    private final BtoAcFireInfoService acFireInfoService;

    /**
     * 获取电池SOC分析数据
     * 
     * 根据逆变器SN和日期时间获取电池的SOC（State of Charge）分析数据，
     * 用于分析电池电量状态变化趋势
     * 
     * @param inverterSn 逆变器序列号
     * @param dateTime 查询日期（格式：yyyy-MM-dd）
     * @return 电池SOC分析数据列表
     */
    @Override
    public List<EnergySocAnalyzeVO> getSocAnalyze(String inverterSn, String dateTime) {
        String replace = dateTime.replace("-", "");
        return btoBatteryDao.getSocAnalyze(inverterSn, "bto_battery_" + replace);
    }

    /**
     * 获取电池图表数据
     * 
     * 根据查询条件获取电池图表数据，包括：
     * - 通过设备服务获取逆变器列表
     * - 根据日期动态生成表名
     * - 查询电池图表数据
     * 
     * @param query 电池查询条件
     * @return 电池图表数据列表
     * @throws BusinessException 当电站不存在逆变器时抛出异常
     */
    @Override
    public List<BatteryVO> chart(BatteryQuery query) {
        // 获取逆变器id
        Result<List<String>> response = deviceServiceClient.getListByType(query.getPlantId(), "1");
        if (response.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            List<String> ids = response.getData();
            if (CollUtil.isEmpty(ids)) {
                throw new BusinessException("电站不存在逆变器");
            } else {
                query.setInverterSn(ids);
            }
        }

        String date = query.getInitTime();
        String tableName = TableUtils.getTableName("bto_battery_", date);
        return btoBatteryDao.chart(tableName, query);
    }

    /**
     * 电池能量分析
     * 
     * 对电池进行能量分析，目前方法返回null，需要后续实现
     * 
     * @param query 电池查询条件
     * @return 电池能量分析数据列表（当前返回null）
     */
    @Override
    public List<BatteryVO> energyAnalysis(BatteryQuery query) {
        return null;
    }

    /**
     * 获取电池保护信息
     * 
     * 通过交流消防信息服务获取电池保护相关信息
     * 
     * @param query 电池查询条件
     * @return 电池保护信息列表
     */
    @Override
    public List<BtoAcFireInfoVO> getBatteryGuardInfo(BatteryQuery query) {
        return acFireInfoService.getBatteryGuardInfo(query);
    }

}
