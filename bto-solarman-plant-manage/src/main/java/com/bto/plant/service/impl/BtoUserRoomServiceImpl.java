package com.bto.plant.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.BtoUserRoomQuery;
import com.bto.commons.pojo.entity.BtoUserRoomEntity;
import com.bto.commons.pojo.vo.BtoUserRoomVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.convert.BtoUserRoomConvert;
import lombok.AllArgsConstructor;
import com.bto.plant.dao.BtoUserRoomDao;
import com.bto.plant.service.BtoUserRoomService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 空间管理服务实现类
 * 
 * 用户房间空间管理相关业务服务的具体实现类，提供用户空间信息查询、增删改查等功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
@Service
@AllArgsConstructor
public class BtoUserRoomServiceImpl extends BaseServiceImpl<BtoUserRoomDao, BtoUserRoomEntity> implements BtoUserRoomService {

    private final GlobalParamUtil globalParamUtil;

    /**
     * 获取用户空间列表
     * 根据用户ID查询该用户的所有空间信息，按排序和时间降序排列
     *
     * @param userUid 用户唯一标识
     * @return 用户空间信息列表
     */ 
    @Override
    public List<BtoUserRoomVO> list(String userUid) {
        globalParamUtil.checkUserPermission(userUid);
        LambdaQueryWrapper<BtoUserRoomEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BtoUserRoomEntity::getUserUid, userUid);
        wrapper.orderByDesc(BtoUserRoomEntity::getOrder);
        wrapper.orderByDesc(BtoUserRoomEntity::getUpdateTime);
        List<BtoUserRoomEntity> btoUserRoomEntities = baseMapper.selectList(wrapper);
        return BtoUserRoomConvert.INSTANCE.convertList(btoUserRoomEntities);
    }

    /**
     * 保存或更新空间信息
     * 处理用户空间信息的保存或更新操作，包含空间名称唯一性校验
     *
     * @param vo 空间信息视图对象
     * @throws BusinessException 当空间名称重复时抛出异常
     */ 
    @Override
    public void handleSaveOrUpdate(BtoUserRoomVO vo) {
        globalParamUtil.checkUserPermission(vo.getUserUid());
        BtoUserRoomEntity entity = BtoUserRoomConvert.INSTANCE.convert(vo);
        if (baseMapper.exists(Wrappers.lambdaQuery(BtoUserRoomEntity.class).eq(BtoUserRoomEntity::getRoom, entity.getRoom()))) {
            throw new BusinessException("空间名称重复");
        }
        if (entity.getId() == null) {
            baseMapper.insert(entity);
        } else {
            updateById(entity);
        }
    }


    /**
     * 删除用户空间
     * 批量删除用户指定的空间信息，包含权限验证
     *
     * @param query 删除查询条件（包含用户ID和空间ID列表）
     */ 
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(BtoUserRoomQuery query) {
        globalParamUtil.checkUserPermission(query.getUserUid());
        removeByIds(query.getIds());
    }
}