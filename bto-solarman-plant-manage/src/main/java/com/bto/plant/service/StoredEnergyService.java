package com.bto.plant.service;

import com.bto.commons.pojo.vo.EnergySocAnalyzeVO;

import java.util.List;

/**
 * 储能服务接口
 * 
 * 储能系统相关业务服务的接口定义，提供储能设备的SOC分析和数据查询功能。
 * 
 * <AUTHOR>
 * @since 2025/2/12
 */
public interface StoredEnergyService {
    
    /**
     * 获取SOC分析数据
     * 
     * 根据逆变器序列号和日期时间获取储能设备的SOC分析数据，
     * 包括电量状态、充放电分析等信息。
     *
     * @param inverterSn 逆变器序列号
     * @param dateTime 日期时间
     * @return SOC分析数据列表
     */
    List<EnergySocAnalyzeVO> getSocAnalyze(String inverterSn, String dateTime);
}
