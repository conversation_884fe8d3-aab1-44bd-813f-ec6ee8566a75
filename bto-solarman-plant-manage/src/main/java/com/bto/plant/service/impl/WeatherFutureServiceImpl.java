package com.bto.plant.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.pojo.entity.WeatherFutureEntity;
import com.bto.commons.pojo.vo.WeatherFutureVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.convert.WeatherFutureConvert;
import com.bto.plant.dao.WeatherFutureDao;
import com.bto.plant.service.WeatherFutureService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 未来天气服务实现类
 * 提供未来15天天气预报数据查询服务，支持按城市和日期范围获取天气信息。
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-05
 */
@Service
@AllArgsConstructor
public class WeatherFutureServiceImpl extends BaseServiceImpl<WeatherFutureDao, WeatherFutureEntity> implements WeatherFutureService {

    /**
     * 获取未来天气列表
     * 根据指定的日期列表和城市列表查询未来天气预报数据
     *
     * @param futureDateList 未来日期列表
     * @param city 城市列表
     * @return 未来天气信息列表
     */ 
    @Override
    @DS("slave")
    public List<WeatherFutureVO> getFutureWeatherList(List<LocalDate> futureDateList, List<String> city) {
        LambdaQueryWrapper<WeatherFutureEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(WeatherFutureEntity::getCity, city);
        wrapper.in(WeatherFutureEntity::getCollectDate, futureDateList);
        List<WeatherFutureEntity> weatherFutureEntities = baseMapper.selectList(wrapper);
        return WeatherFutureConvert.INSTANCE.convertList(weatherFutureEntities);
    }
}