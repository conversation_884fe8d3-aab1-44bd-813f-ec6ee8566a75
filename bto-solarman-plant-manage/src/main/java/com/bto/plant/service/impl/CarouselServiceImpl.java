package com.bto.plant.service.impl;

import com.bto.commons.constant.DeviceStatus;
import com.bto.commons.pojo.dto.PlantAddressDTO;
import com.bto.commons.pojo.dto.PlantCarouselDTO;
import com.bto.commons.pojo.dto.RankWorkEfficiencyQueryDTO;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.plant.dao.CarouselMapper;
import com.bto.plant.service.CarouselService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 轮播图服务实现类
 * 
 * 电站轮播图相关业务服务的具体实现类，提供电站工作效率排名、轮播数据获取、电站坐标信息查询等核心功能。
 * 为电站管理和展示提供数据支撑和可视化服务。
 *
 * <AUTHOR>
 * @date 2023/4/21 15:53
 */
@Service
public class CarouselServiceImpl implements CarouselService {
    @Autowired
    private CarouselMapper carouselMapper;

    /**
     * 获取电站工作效率排名
     * 
     * 根据查询条件和用户信息获取电站的工作效率排名数据
     * 
     * @param query 查询条件
     * @param userInfo 用户信息
     * @return 工作效率VO列表
     */
    @Override
    public List<WorkEfficiencyVO> getRankWorkEfficiency(RankWorkEfficiencyQueryDTO query, RequireParamsDTO userInfo) {
        List<WorkEfficiencyVO> rankWorkEfficiency = carouselMapper.getRankWorkEfficiency(query, userInfo);
        return rankWorkEfficiency;
    }


    /**
     * 获取电站轮播数据
     * 
     * 根据起始索引和用户信息获取电站轮播数据，支持循环轮播和范围查询。
     * 该方法会计算电站的工作效率和离线时间，为前端展示提供完整的数据支撑。
     * 
     * 数据处理流程：
     * - 根据索引范围获取电站列表
     * - 计算每个电站的工作效率百分比
     * - 计算离线设备的离线时长（天/小时格式）
     * - 支持循环轮播（当起始索引大于结束索引时）
     * 
     * @param startIndex 起始索引，从0开始计数
     * @param endIndex 结束索引，不包含该索引位置的电站
     * @param userInfo 用户信息，用于权限验证和数据过滤
     * @return 电站轮播DTO，包含指定范围内的电站列表和总数统计
     */    
    @Override
    public PlantCarouselDTO getPlantCarousel(String startIndex, String endIndex, RequireParamsDTO userInfo) {

        Integer start = Integer.valueOf(startIndex);
        Integer end = Integer.valueOf(endIndex);

        PlantCarouselDTO result = new PlantCarouselDTO();
        List<PlantCarouselVO> resultList = new ArrayList<>();


        List<PlantCarouselVO> plantList = carouselMapper.selectPlantList(userInfo);
        if (start < end) {
            for (int i = start; i < end && i < plantList.size(); i++) {
                plantList.get(i).setIndex(i + 1);
                resultList.add(plantList.get(i));
            }
        } else {
            for (int i = start; i < plantList.size(); i++) {
                plantList.get(i).setIndex(i + 1);
                resultList.add(plantList.get(i));
            }
            for (int i = 0; i < Math.min(end, plantList.size()); i++) {
                plantList.get(i).setIndex(i + 1);
                resultList.add(plantList.get(i));
            }
        }

        // 计算电站效率与离线时间
        LocalDateTime now = LocalDateTime.now();
        // 定义updateTime字符串的日期/时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (PlantCarouselVO plantCarouselDTO : resultList) {
            String workEfficiency = BusinessCalculateUtil.getWorkEfficiency(plantCarouselDTO.getPower(), plantCarouselDTO.getPlantCapacity().multiply(BigDecimal.valueOf(1000)).toString());
            plantCarouselDTO.setPlantEfficiency(workEfficiency);
            if (DeviceStatus.OFFLINE.getName().equals(plantCarouselDTO.getStatus())) {
                // 将updateTime字符串解析为LocalDateTime对象
                LocalDateTime updateTime = LocalDateTime.parse(plantCarouselDTO.getUpdateTime(), formatter);
                // 计算updateTime和当前时间之间的小时差异
                Duration duration = Duration.between(updateTime, now);
                long hoursDiff = duration.toHours();
                // 计算天数差异
                long daysDiff = hoursDiff / 24;
                // 剩余的小时数
                hoursDiff = hoursDiff % 24;
                String timeDiff;
                timeDiff = hoursDiff == 0 ? "-" : daysDiff + "天" + String.format("%02d", hoursDiff) + "小时";
                plantCarouselDTO.setOfflineTime(timeDiff);
            } else {
                String timeDiff = "-";
                plantCarouselDTO.setOfflineTime(timeDiff);
            }
        }
        result.setRecords(resultList);
        result.setTotal(plantList.size());

        return result;
    }

    /**
     * 获取电站坐标信息
     * 
     * 根据地址查询条件和用户信息获取电站的坐标信息
     * 
     * @param plantAddressDTO 地址查询条件
     * @param userInfo 用户信息
     * @return 电站坐标信息列表
     */
    @Override
    public List<PlantCoordinateInfoVO> getPlantCoordinate(PlantAddressDTO plantAddressDTO, RequireParamsDTO userInfo) {
        List<PlantCoordinateInfoVO> plantCoordinate = carouselMapper.getPlantCoordinate(plantAddressDTO, userInfo);
        return plantCoordinate;
    }

    /**
     * 获取区域电站坐标信息
     * 
     * 根据地址查询条件和用户信息获取指定区域内的电站坐标信息
     * 
     * @param plantAddressDTO 地址查询条件
     * @param userInfo 用户信息
     * @return 区域电站坐标信息列表
     */
    @Override
    public List<AreaCoordinateInfoVO> getPlantInfoCoordinateOfArea(PlantAddressDTO plantAddressDTO, RequireParamsDTO userInfo) {
        List<AreaCoordinateInfoVO> result = carouselMapper.getPlantInfoCoordinateOfArea(plantAddressDTO, userInfo);
        return result;
    }
}
