package com.bto.plant.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.RepairRecordsQuery;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.entity.RepairRecordsEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.RepairRecordsVO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.convert.RepairRecordsConvert;
import com.bto.plant.dao.RepairRecordsDao;
import com.bto.plant.service.PlantService;
import com.bto.plant.service.RepairRecordsService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.bto.commons.utils.SorterUtils.getPage;

/**
 * 维修记录服务实现类
 * 
 * 维修记录相关业务服务的具体实现类，提供维修记录的分页查询、增删改查等功能
 *
 * @since 2024-04-29
 */
@Service
@AllArgsConstructor
public class RepairRecordsServiceImpl extends BaseServiceImpl<RepairRecordsDao, RepairRecordsEntity> implements RepairRecordsService {
    public final RepairRecordsDao repairRecordsDao;
    public final GlobalParamUtil globalParamUtil;
    public final PlantService plantService;

    /**
     * 分页查询维修记录
     * 
     * <p>根据查询条件获取维修记录的分页数据，支持按维护日期排序。</p>
     *
     * @param query 查询条件（包含分页参数、过滤条件等）
     * @return 维修记录分页结果
     */
    @Override
    public PageResult<RepairRecordsVO> page(RepairRecordsQuery query) {
        if (StrUtil.isBlank(query.getOrder())) {
            query.setOrder("maintainDate");
        }
        Page<RepairRecordsVO> page = getPage(query);
        IPage<RepairRecordsVO> result = repairRecordsDao.page(page, query);
        return new PageResult<>(result.getRecords(), result.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 
     * <p>根据查询条件构建MyBatis-Plus查询条件包装器，支持多条件组合查询。</p>
     *
     * @param query 查询条件
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<RepairRecordsEntity> getWrapper(RepairRecordsQuery query) {
        LambdaQueryWrapper<RepairRecordsEntity> wrapper = Wrappers.lambdaQuery();
        wrapper
                .eq(StrUtil.isNotBlank(query.getId()), RepairRecordsEntity::getId, query.getId())
                .eq(StrUtil.isNotBlank(query.getPlantUid()), RepairRecordsEntity::getPlantUid, query.getPlantUid())
                .eq(StrUtil.isNotBlank(query.getDeviceIdOld()), RepairRecordsEntity::getDeviceIdOld, query.getDeviceIdOld())
                .eq(StrUtil.isNotBlank(query.getDeviceIdNew()), RepairRecordsEntity::getDeviceIdNew, query.getDeviceIdNew())
                .eq(StrUtil.isNotBlank(query.getDeviceType()), RepairRecordsEntity::getDeviceType, query.getDeviceType())
                .eq(StrUtil.isNotBlank(query.getServiceman()), RepairRecordsEntity::getServiceman, query.getServiceman())
                .eq(StrUtil.isNotBlank(query.getRemarks()), RepairRecordsEntity::getRemarks, query.getRemarks())
                .like(StrUtil.isNotBlank(query.getMaintain()), RepairRecordsEntity::getMaintain, query.getMaintain())

                .eq(ObjUtil.isNotNull(query.getMaintainDate()), RepairRecordsEntity::getMaintainDate, query.getMaintainDate());
        return wrapper;
    }

    /**
     * 保存维修记录
     * 
     * <p>保存新的维修记录信息，支持根据电站名称自动获取电站UID，
     * 并将视图对象转换为实体对象后保存到数据库。</p>
     *
     * @param vo 维修记录视图对象
     * @throws BusinessException 当电站不存在时抛出异常
     */
    @Override
    public void save(RepairRecordsVO vo) {
        if (StrUtil.isBlank(vo.getPlantUid())){
            Plant plant = plantService.getPlantInfoByPlantName(vo.getPlantName());
            if (plant == null) {
                throw new BusinessException(ResultEnum.OPERATION_FAILED, "电站不存在");
            }
            vo.setPlantUid(plant.getPlantUid());
        }

        RepairRecordsEntity entity = RepairRecordsConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    /**
     * 更新维修记录
     * 
     * <p>更新现有的维修记录信息，将视图对象转换为实体对象后更新到数据库。</p>
     *
     * @param vo 维修记录视图对象
     */
    @Override
    public void update(RepairRecordsVO vo) {
        RepairRecordsEntity entity = RepairRecordsConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    /**
     * 批量删除维修记录
     * 
     * <p>根据ID列表批量删除维修记录，支持事务回滚。</p>
     *
     * @param idList 维修记录ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

}