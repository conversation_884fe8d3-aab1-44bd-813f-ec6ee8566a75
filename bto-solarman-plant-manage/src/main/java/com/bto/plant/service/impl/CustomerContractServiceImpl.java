package com.bto.plant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.CustomerContractQueryDTO;
import com.bto.commons.pojo.vo.CustomerContractInfoVO;
import com.bto.commons.pojo.vo.CustomerContractVO;
import com.bto.commons.pojo.vo.RequireParamsDTO;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.CustomerContractMapper;
import com.bto.plant.service.AsyncService;
import com.bto.plant.service.CustomerContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.bto.commons.response.ResultEnum.OPERATION_FAILED;

/**
 * 客户合同服务实现类
 * 客户合同相关业务服务的具体实现类，提供合同信息管理、批量插入、数据去重、合同编号管理等功能。支持并发处理和事务管理。
 *
 * <AUTHOR>
 * @date 2023/8/8 15:45
 */
@Service
@Slf4j
public class CustomerContractServiceImpl implements CustomerContractService {
    @Autowired
    private CustomerContractMapper customerContractMapper;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private GlobalParamUtil globalParamUtil;
    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 获取客户合同信息
     *
     * 根据查询条件和用户信息分页获取客户合同信息列表
     *
     * @param query 客户合同查询条件数据传输对象
     * @param userInfo 用户信息参数对象，包含用户相关权限信息
     * @return IPage<CustomerContractVO> 分页结果对象，包含客户合同视图信息
     */
    @Override
    public IPage<CustomerContractVO> getCustomerContractInfo(CustomerContractQueryDTO query, RequireParamsDTO userInfo) {
        IPage<CustomerContractVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        IPage<CustomerContractVO> iPage = customerContractMapper.selectPage(page, userInfo,query);
        iPage.getRecords().forEach(record -> {
            record.setProjectName(globalParamUtil.getProjectNameByProjectId(record.getProjectId().toString()));
            record.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(record.getPlantCapacity()));
        });
        return iPage;
    }

    /**
     * 新增客户合同信息
     *
     * 保存新的客户合同信息到数据库
     *
     * @param contractInfo 客户合同信息视图对象，包含需要保存的合同信息
     */
    @Override
    public void addCustomerContractInfo(CustomerContractInfoVO contractInfo) {
        LambdaQueryWrapper<CustomerContractVO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerContractVO::getContractId, contractInfo.getContractId());
        if (customerContractMapper.exists(queryWrapper)) {
            throw new BusinessException(OPERATION_FAILED, "合同编号已存在!");
        }
        queryWrapper.clear();
        queryWrapper.eq(CustomerContractVO::getPlantName, contractInfo.getPlantName());
        if (customerContractMapper.exists(queryWrapper)) {
            throw new BusinessException(OPERATION_FAILED, "电站名称已存在!");
        }
        CustomerContractVO entity = new CustomerContractVO();
        entity.setCreateTime(DateTime.now().toString());
        BeanUtil.copyProperties(contractInfo, entity, true);
        try {
            customerContractMapper.insert(entity);
        } catch (Exception e) {
            throw new BusinessException(OPERATION_FAILED, e.getMessage());
        }

    }

    /**
     * 批量插入客户合同信息
     *
     * 批量保存客户合同信息到数据库，支持并发插入
     *
     * @param contractInfoList 客户合同信息视图对象列表，包含需要批量保存的合同信息
     * @return Integer 成功插入的记录数量
     * @throws IllegalAccessException 当访问权限异常时抛出
     * @throws InterruptedException 当线程被中断时抛出
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Integer batchInsertCustomerContractInfo(List<CustomerContractInfoVO> contractInfoList) throws IllegalAccessException, InterruptedException {
        List<CustomerContractVO> customerContracts = new ArrayList<>();
        //校验contractInfoList的数据
        for (CustomerContractInfoVO customerContractInfoVO : contractInfoList) {
            // if (ObjectUtils.objectParamsIsNull(customerContractInfoVO)) {
            if (Objects.isNull(customerContractInfoVO)) {
                throw new BusinessException(ResultEnum.REQUEST_PARAM_NULL_ERROR);
            }
            //将前端数据处理成所需要的数据实体
            CustomerContractVO entity = new CustomerContractVO();
            BeanUtil.copyProperties(customerContractInfoVO, entity);
            entity.setCreateTime(DateTime.now().toString());
            entity.setUpdateTime(DateTime.now().toString());
            customerContracts.add(entity);
        }
        List<CustomerContractVO> resultList = reduceContractList(customerContracts);
        if (CollUtil.isEmpty(resultList)) {
            throw new BusinessException(ResultEnum.DATA_EXISTED_ERROR);
        }
        List<List<CustomerContractVO>> lists = ListUtil.split(resultList, 2000);
        int threadNum = lists.size();
        // 是否存在异常
        AtomicReference<Boolean> rollbackFlag = new AtomicReference<>(false);
        CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        long start = System.currentTimeMillis();
        for (List<CustomerContractVO> list : lists) {
            asyncService.executeAsync(list, customerContractMapper, countDownLatch, rollbackFlag);
        }
        long end = System.currentTimeMillis();

        try {
            if (!countDownLatch.await(2000L, TimeUnit.SECONDS)) {
                log.info("执行时间过长!!");
                rollbackFlag.set(true);
            }
        } catch (Exception e) {
            log.info("批量新增失败！");
            throw new BusinessException(ResultEnum.BATCH_INSERT_ERROR);
        }
        if (rollbackFlag.get()) {
            return 0;
        } else {
            return resultList.size();
        }
    }

    /**
     * 客户合同信息数据去重
     *
     * 对合同信息列表进行去重处理，确保数据的唯一性
     *
     * @param contractInfoList 客户合同信息视图对象列表，需要去重的合同数据
     * @return List<CustomerContractVO> 去重后的客户合同视图对象列表
     */
    @Override
    public List<CustomerContractVO> reduceContractList(List<CustomerContractVO> contractInfoList) {
        QueryWrapper<CustomerContractVO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted", "0");
        List<CustomerContractVO> reduceList = customerContractMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(reduceList)) {
            List<String> reduceIdList = reduceList.stream().map(CustomerContractVO::getContractId).collect(Collectors.toList());
            List<CustomerContractVO> resultList = contractInfoList.stream().filter(customerContract -> {
                return !reduceIdList.contains(customerContract.getContractId());
            }).collect(Collectors.toList());
            return resultList;
        }
        return contractInfoList;
    }

    /**
     * 新增博通合同编号
     *
     * 根据查询条件为合同新增博通合同编号
     *
     * @param query 客户合同查询条件数据传输对象
     */
    @Override
    public void addBtoContractNumber(CustomerContractQueryDTO query) {
        if (!Objects.isNull(query.getPlantCapacity())){
            BigDecimal bigDecimal = new BigDecimal(query.getPlantCapacity());
            // 乘1000
            query.setPlantCapacity(bigDecimal.multiply(new BigDecimal(1000)).toString());
        }
        customerContractMapper.addBtoContractNumber(query);
    }
}
