package com.bto.plant.service.impl;

import com.bto.commons.pojo.entity.BtoSwitchTempEntity;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.BtoSwitchTempDao;
import lombok.AllArgsConstructor;
import com.bto.plant.service.BtoSwitchTempService;
import org.springframework.stereotype.Service;

/**
 * 配电箱(开关)温度监控数据服务实现类
 * 
 * 配电箱和开关温度监控数据相关业务服务的具体实现类，提供配电设备温度状态的监控和管理功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2025-06-18
 */
@Service
@AllArgsConstructor
public class BtoSwitchTempServiceImpl extends BaseServiceImpl<BtoSwitchTempDao, BtoSwitchTempEntity> implements BtoSwitchTempService {

}