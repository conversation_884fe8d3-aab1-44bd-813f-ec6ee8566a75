package com.bto.plant.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.pojo.dto.BatteryQuery;
import com.bto.commons.pojo.entity.BtoAcFireInfoEntity;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.convert.BtoAcFireInfoConvert;
import lombok.AllArgsConstructor;
import com.bto.plant.dao.BtoAcFireInfoDao;
import com.bto.plant.service.BtoAcFireInfoService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 空调消防信息(实时)服务实现类
 * 
 * 空调消防设备实时数据相关业务服务的具体实现类，提供空调消防设备的实时信息查询功能。
 * 主要服务于储能电站的空调和消防系统监控场景，支持单逆变器和多逆变器场景的数据查询。
 * 
 * <AUTHOR>
 * @since 1.0.0 2025-04-22
 */
@Service
@AllArgsConstructor
public class BtoAcFireInfoServiceImpl extends BaseServiceImpl<BtoAcFireInfoDao, BtoAcFireInfoEntity> implements BtoAcFireInfoService {

    /**
     * 获取电池保护信息
     * 
     * 根据查询条件获取空调消防设备的实时保护信息数据，支持单逆变器和多逆变器场景的数据查询。
     * 对于单逆变器场景，返回当日完整的保护信息数据；对于多逆变器场景（工商业），目前返回null（待后续实现）。
     * 
     * @param query 电池查询条件（包含逆变器SN列表）
     * @return 空调消防信息视图列表，单逆变器返回当日数据，多逆变器返回null（待实现）
     */ 
    @Override
    public List<BtoAcFireInfoVO> getBatteryGuardInfo(BatteryQuery query) {
        Date date = new Date();
        List<String> inverterSn = query.getInverterSn();
        if (inverterSn != null && inverterSn.size() == 1) {
            LambdaQueryWrapper<BtoAcFireInfoEntity> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(BtoAcFireInfoEntity::getInverterSn, inverterSn.get(0))
                    .ge(BtoAcFireInfoEntity::getInitTime, DateUtil.beginOfDay(date))
                    .lt(BtoAcFireInfoEntity::getInitTime, DateUtil.endOfDay(date));
            List<BtoAcFireInfoEntity> btoAcFireInfoEntities = baseMapper.selectList(wrapper);
            return BtoAcFireInfoConvert.INSTANCE.convertList(btoAcFireInfoEntities);
        } else {
            // 工商业/多逆变器
        }
        return null;
    }
}