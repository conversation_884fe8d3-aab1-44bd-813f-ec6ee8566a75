package com.bto.plant.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bto.commons.pojo.dto.ForecastPlantDTO;
import com.bto.commons.pojo.dto.PlantAddressDTO;
import com.bto.commons.pojo.dto.PlantVO;
import com.bto.commons.pojo.dto.RealityForecastElectricityDTO;
import com.bto.commons.pojo.entity.MeteorologyEntity;
import com.bto.commons.pojo.vo.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * 预测服务接口
 * 
 * 提供电站发电预测、天气数据获取、辐射量计算、历史数据分析等
 * 与电站发电预测相关的业务服务
 *
 * <AUTHOR> by zhb on 2023/12/21.
 */
public interface ForecastService {

    /**
     * 获取电站基础信息
     * 
     * 根据电站信息和预测基础信息获取电站的完整基础信息
     *
     * @param plantVO 电站视图对象，包含电站基本信息
     * @param forecastBaseInfoVO 预测基础信息视图对象
     * @return ForecastBaseInfoVO 电站预测基础信息
     */
    ForecastBaseInfoVO getPlantBaseInfo(PlantVO plantVO, ForecastBaseInfoVO forecastBaseInfoVO);

    /**
     * 根据地址获取辐射量
     * 
     * 根据电站地址信息和指定日期计算太阳辐射量
     *
     * @param plantAddressDTO 电站地址数据传输对象
     * @param dateTime 指定日期时间
     * @return Double 太阳辐射量数值
     */
    Double getRadiationByAddress(PlantAddressDTO plantAddressDTO, Date dateTime);

    /**
     * 根据城市获取辐射量
     * 
     * 根据城市名称和指定日期获取该城市的太阳辐射量
     *
     * @param city 城市名称
     * @param dateTime 指定日期时间
     * @return Double 太阳辐射量数值
     */
    Double getRadiationByCity(String city, Date dateTime);

    /**
     * 获取未来预测数据
     * 
     * 根据电站ID、辐射量和指定日期计算未来发电预测数据
     *
     * @param plantId 电站唯一标识符
     * @param radiation 太阳辐射量
     * @param dateTime 指定日期时间
     * @return BatteryDivinerVO 电池预测数据视图对象
     */
    BatteryDivinerVO getFutureForecastData(String plantId, Double radiation, Date dateTime);

    /**
     * 根据城市获取历史电量
     * 
     * 根据城市名称获取该城市历史发电电量数据
     *
     * @param city 城市名称
     * @return List<BatteryDivinerVO> 电池预测数据视图对象列表
     */
    List<BatteryDivinerVO> getHistoryElectricityByCity(String city);

    /**
     * 根据城市获取日期辐射量
     * 
     * 使用主数据源，根据城市名称获取每日辐射量数据
     *
     * @param city 城市名称
     * @return HashMap<String, List<DateRadiationVO>> 日期辐射量映射
     */
    @DS("master")
    HashMap<String, List<DateRadiationVO>> getDateRadiationByCity(String city);

    /**
     * 根据城市获取未来预测
     * 
     * 根据城市名称和城市辐射量获取未来发电预测结果
     *
     * @param city 城市名称
     * @param cityRadiation 城市辐射量
     * @return String 预测结果字符串
     */
    String getFutureForecastByCity(String city, Double cityRadiation);

    /**
     * 根据城市获取天气
     * 
     * 根据城市名称获取该城市的天气预报信息
     *
     * @param city 城市名称
     * @return HashMap<String, List<WeatherFutureVO>> 天气预测映射
     */
    HashMap<String, List<WeatherFutureVO>> getWeatherByCity(String city);

    /**
     * 根据城市获取电量
     * 
     * 根据城市名称获取该城市的电量数据
     *
     * @param city 城市名称
     * @return HashMap<String, List<BatteryDivinerVO>> 电量数据映射
     */
    HashMap<String, List<BatteryDivinerVO>> getElectricityByCity(String city);

    /**
     * 项目电量预测
     * 
     * 根据项目ID和预测天数进行电量预测
     *
     * @param projectId 项目唯一标识符
     * @param size 预测天数
     * @return AbstractMap<String, String> 预测结果映射
     */
    AbstractMap<String, String> predictionElectByProject(String projectId,int size);

    /**
     * 电量预测分析
     * 
     * 根据项目ID进行电量预测分析
     *
     * @param projectId 项目唯一标识符
     * @return Map<String, Integer> 分析结果映射
     */
    Map<String, Integer> predictionElectAnalysis(String projectId);

    /**
     * 电量预测
     * 
     * 根据项目ID进行电量预测计算
     *
     * @param projectId 项目唯一标识符
     * @return HashMap<String, BigDecimal> 预测电量结果映射
     */
    HashMap<String, BigDecimal> predictionElectricity(String projectId);

    /**
     * 获取历史辐射量列表
     * 
     * 根据日期列表和城市获取历史太阳辐射量数据
     *
     * @param dates 日期列表
     * @param city 城市名称
     * @return List<DateRadiationVO> 日期辐射量视图对象列表
     */
    List<DateRadiationVO> getHistoryRadiationList(List<LocalDate> dates, String city);

    /**
     * 获取预测电站列表
     * 
     * 根据查询条件分页获取预测相关的电站列表
     *
     * @param forForecastPlantDTO 预测电站查询数据传输对象
     * @return IPage<ForecastPlantVO> 分页结果对象
     */
    IPage<ForecastPlantVO> getForecastPlantList(ForecastPlantDTO forForecastPlantDTO);

    /**
     * 获取等级评估电站列表
     * 
     * 根据查询条件获取等级评估相关的电站列表信息
     *
     * @param query 查询条件对象
     * @return HashMap<String, Object> 等级评估结果映射
     */
    HashMap<String, Object> getGradeEvaluationPlantList(ForecastPlantDTO query);

    /**
     * 获取当前预测电量
     * 
     * 根据电站ID获取当前的发电预测电量
     *
     * @param plantId 电站唯一标识符
     * @return String 当前预测电量结果
     */
    String getCurrentForecastElectricity(String plantId);

    /**
     * 获取评估图表数据
     * 
     * 根据电站ID和日期获取电站发电评估图表数据
     *
     * @param plantId 电站唯一标识符
     * @param date 查询日期
     * @return HashMap<String, List<PlantPowerVO>> 评估图表数据映射
     */
    HashMap<String, List<PlantPowerVO>> getEvaluateChart(String plantId, String date);

    /**
     * 获取天气数据
     * 
     * 根据电站ID获取该电站的天气数据信息
     *
     * @param plantId 电站唯一标识符
     * @return WeatherDataVO 天气数据视图对象
     */
    WeatherDataVO getWeatherData(String plantId);

    /**
     * 获取气象数据列表
     * 
     * 根据电站ID和时间范围获取气象数据列表
     *
     * @param plantId 电站唯一标识符
     * @param begin 开始日期
     * @param end 结束日期
     * @return List<MeteorologyEntity> 气象实体对象列表
     */
    List<MeteorologyEntity> getMeteorologyListByPlantId(String plantId, String begin, String end);

    /**
     * 获取实际预测电量
     * 
     * 根据查询条件获取实际发电量与预测电量的对比数据
     *
     * @param query 实际预测电量查询数据传输对象
     * @return List<RealityForecastElectricityVO> 实际预测电量视图对象列表
     */
    List<RealityForecastElectricityVO> getRealityForecastElectricity(RealityForecastElectricityDTO query);
}