package com.bto.plant.service;

import com.bto.commons.pojo.dto.RepairRecordsQuery;
import com.bto.commons.pojo.entity.RepairRecordsEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.RepairRecordsVO;
import com.bto.commons.service.BaseService;

import java.util.List;

/**
 * 维修记录服务接口
 * 
 * 提供维修记录相关的业务服务，包括维修信息管理、分页查询、增删改查等功能
 *
 * <AUTHOR> 
 * @since  2024-04-29
 */
public interface RepairRecordsService extends BaseService<RepairRecordsEntity> {

    /**
     * 分页查询维修记录
     * 
     * 根据查询条件分页获取维修记录信息列表
     *
     * @param query 维修记录查询条件对象，包含分页和过滤参数
     * @return PageResult<RepairRecordsVO> 分页结果对象，包含维修记录视图信息
     */
    PageResult<RepairRecordsVO> page(RepairRecordsQuery query);

    /**
     * 保存维修记录
     * 
     * 保存新的维修记录信息到数据库
     *
     * @param vo 维修记录信息视图对象，包含需要保存的维修信息
     */
    void save(RepairRecordsVO vo);

    /**
     * 更新维修记录
     * 
     * 更新已存在的维修记录信息
     *
     * @param vo 维修记录信息视图对象，包含需要更新的维修信息
     */
    void update(RepairRecordsVO vo);

    /**
     * 删除维修记录
     * 
     * 根据ID列表批量删除维修记录信息
     *
     * @param idList 维修记录ID列表，指定需要删除的维修记录
     */
    void delete(List<Long> idList);
}