package com.bto.plant.service.impl;

import com.bto.commons.pojo.entity.PlantInfoEntity;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.PlantInfoMapper;
import com.bto.plant.service.BtoPlantInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 电站信息服务实现类
 * 
 * 电站信息补充数据相关业务服务的具体实现类，提供电站基础信息的持久化和管理功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-04
 */
@Service
@AllArgsConstructor
public class BtoPlantInfoServiceImpl extends BaseServiceImpl<PlantInfoMapper, PlantInfoEntity> implements BtoPlantInfoService {

}