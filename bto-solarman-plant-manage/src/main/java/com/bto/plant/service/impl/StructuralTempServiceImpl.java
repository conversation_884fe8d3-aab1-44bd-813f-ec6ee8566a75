package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.StructuralInspectionQuery;
import com.bto.commons.pojo.entity.BtoStructureDataEntity;
import com.bto.commons.pojo.entity.BtoSwitchTempEntity;
import com.bto.commons.pojo.vo.BtoStructureDataVO;
import com.bto.commons.pojo.vo.BtoSwitchTempVO;
import com.bto.commons.response.ResultEnum;
import com.bto.plant.convert.BtoStructureDataConvert;
import com.bto.plant.convert.BtoSwitchTempConvert;
import com.bto.plant.service.BtoStructureDataService;
import com.bto.plant.service.BtoSwitchTempService;
import com.bto.plant.service.StructuralTempService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * 结构检测数据服务实现类
 * 
 * 结构检测数据相关业务服务的具体实现类，提供结构检测数据和开关温度数据的查询和管理功能
 *
 * <AUTHOR> by bto on 2025/6/18.
 */
@Service
@AllArgsConstructor
public class StructuralTempServiceImpl implements StructuralTempService {

    private final BtoSwitchTempService switchTempService;

    private final BtoStructureDataService structureDataService;

    /**
     * 查询结构检测数据列表
     * 
     * 根据查询条件获取结构检测数据列表，支持按设备ID和时间范围过滤。
     *
     * @param query 查询条件（包含设备ID列表、时间范围等）
     * @return 结构检测数据视图对象列表
     */
    public List<BtoStructureDataVO> structuralList(StructuralInspectionQuery query) {
        QueryWrapper<BtoStructureDataEntity> queryWrapper = new QueryWrapper<>();

        if (CollUtil.isNotEmpty(query.getStructureIds())) {
            queryWrapper.lambda()
                    .in(BtoStructureDataEntity::getDeviceId, query.getStructureIds())
                    .between(BtoStructureDataEntity::getReportTime, DateUtil.parse(query.getStartTime()), DateUtil.endOfDay(DateUtil.parse(query.getEndTime())));
            List<BtoStructureDataEntity> list = structureDataService.list(queryWrapper);
            return BtoStructureDataConvert.INSTANCE.convertList(list);
        }
        return Collections.emptyList();
    }

    /**
     * 查询开关温度数据列表
     * 
     * 根据查询条件获取开关温度数据列表，支持按设备ID和时间范围过滤
     *
     * @param query 查询条件（包含设备ID、时间范围等）
     * @return 开关温度数据视图对象列表
     */
    public List<BtoSwitchTempVO> tempList(StructuralInspectionQuery query) {
        if (query.getTempId() == null) {
            return Collections.emptyList();
        }
        QueryWrapper<BtoSwitchTempEntity> queryWrapper = new QueryWrapper<>();

        queryWrapper.lambda()
                .eq(BtoSwitchTempEntity::getDeviceId, query.getTempId())
                .between(BtoSwitchTempEntity::getReportTime, DateUtil.parse(query.getStartTime()), DateUtil.endOfDay(DateUtil.parse(query.getEndTime())));
        List<BtoSwitchTempEntity> list = switchTempService.list(queryWrapper);
        return BtoSwitchTempConvert.INSTANCE.convertList(list);
    }

    /**
     * 获取最新结构检测数据
     * 
     * 根据结构检测的设备ID列表获取每个设备的最新检测数据
     *
     * @param structureIds 结构检测设备ID列表
     * @return 最新结构检测数据视图对象列表
     */
    public List<BtoStructureDataVO> getStructureByLatest(List<Long> structureIds) {
        ArrayList<BtoStructureDataVO> vos = new ArrayList<>();
        if (CollUtil.isNotEmpty(structureIds)) {
            for (Long structureId : structureIds) {
                QueryWrapper<BtoStructureDataEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(BtoStructureDataEntity::getDeviceId, structureId)
                        .orderByDesc(BtoStructureDataEntity::getReportTime)
                        .last("limit 1");
                BtoStructureDataEntity one = structureDataService.getOne(queryWrapper);
                if (one != null) {
                    vos.add(BtoStructureDataConvert.INSTANCE.convert(one));
                }
            }
            return vos;
        }
        return Collections.emptyList();
    }

    /**
     * 获取最新开关温度数据
     * 
     * 根据设备ID获取最新的开关温度数据
     *
     * @param deviceId 设备ID
     * @return 最新开关温度数据视图对象
     */
    public BtoSwitchTempVO getSwitchTempByLatest(Long deviceId) {
        QueryWrapper<BtoSwitchTempEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BtoSwitchTempEntity::getDeviceId, deviceId)
                .orderByDesc(BtoSwitchTempEntity::getReportTime)
                .last("limit 1");
        BtoSwitchTempEntity one = switchTempService.getOne(queryWrapper);
        return BtoSwitchTempConvert.INSTANCE.convert(one);
    }

    /**
     * 查询最新检测数据
     * 
     * 根据查询类型和设备ID获取最新的结构检测或开关温度数据
     *
     * @param query 查询条件（包含查询类型、设备ID等）
     * @return 最新检测数据
     * @throws BusinessException 当查询类型无效时抛出异常
     */
    @Override
    public Object queryLatest(StructuralInspectionQuery query) {
        if (DeviceType.STRUCTURE.getFieldName().equals(query.getQueryType())) {
            return getStructureByLatest(query.getStructureIds());
        } else if (DeviceType.TEMP.getFieldName().equals(query.getQueryType())) {
            return getSwitchTempByLatest(query.getTempId());
        } else {
            throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR);
        }
    }

    /**
     * 查询检测数据列表
     * 
     * 根据查询类型获取结构检测或开关温度的历史数据列表
     *
     * @param query 查询条件（包含查询类型、时间范围等）
     * @return 检测数据列表
     * @throws BusinessException 当时间参数无效或查询类型无效时抛出异常
     */
    @Override
    public Object selectList(StructuralInspectionQuery query) {
        // 判断开始时间和结束时间不能为空
        if (query.getStartTime() == null || query.getEndTime() == null) {
            throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR);
        }
        if (DeviceType.STRUCTURE.getFieldName().equals(query.getQueryType())) {
            return structuralList(query);
        } else if (DeviceType.TEMP.getFieldName().equals(query.getQueryType())) {
            return tempList(query);
        } else {
            throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR);
        }
    }
}