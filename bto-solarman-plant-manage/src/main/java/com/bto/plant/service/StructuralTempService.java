package com.bto.plant.service;

import com.bto.commons.pojo.dto.StructuralInspectionQuery;

/**
 * 结构检测数据服务接口
 * 
 * 结构检测数据相关业务服务的接口定义，提供结构检测数据的查询和最新数据获取功能。
 * 
 * 支持结构检测数据的列表查询和最新检测数据查询，为电站结构安全监测提供数据支持。
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-06-18
 */
public interface StructuralTempService {

    /**
     * 查询结构检测数据列表
     * 
     * 根据查询条件获取结构检测数据列表，支持多条件过滤和分页查询。
     *
     * @param query 查询条件
     * @return 结构检测数据列表
     */
    Object selectList(StructuralInspectionQuery query);

    /**
     * 查询最新结构检测数据
     * 
     * 根据查询条件获取最新的结构检测数据，用于实时监控和状态分析。
     *
     * @param query 查询条件
     * @return 最新结构检测数据
     */
    Object queryLatest(StructuralInspectionQuery query);
}