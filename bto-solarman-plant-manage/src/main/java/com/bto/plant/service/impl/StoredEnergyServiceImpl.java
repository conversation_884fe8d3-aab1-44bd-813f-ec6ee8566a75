package com.bto.plant.service.impl;

import com.bto.commons.pojo.vo.EnergySocAnalyzeVO;
import com.bto.plant.service.BtoBatteryService;
import com.bto.plant.service.StoredEnergyService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 储能服务实现类
 * 储能系统相关业务服务的具体实现类，提供储能设备SOC（荷电状态）分析功能。
 * 主要服务于储能电站的运行监控和数据分析场景。
 *
 * <AUTHOR> by zhb on 2025/2/12.
 */
@Service
@AllArgsConstructor
public class StoredEnergyServiceImpl implements StoredEnergyService {

    private final BtoBatteryService batteryService;

    /**
     * 获取SOC分析数据
     * 
     * @param inverterSn 逆变器SN号
     * @param dateTime 日期时间
     * @return SOC分析数据列表
     */ 
    @Override
    public List<EnergySocAnalyzeVO> getSocAnalyze(String inverterSn, String dateTime) {
        return batteryService.getSocAnalyze(inverterSn, dateTime);
    }

}
