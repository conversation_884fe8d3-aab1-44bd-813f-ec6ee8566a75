package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.PlantEvaluationQuery;
import com.bto.commons.pojo.dto.PlantVO;
import com.bto.commons.pojo.dto.RadiantQuantityQuery;
import com.bto.commons.pojo.entity.CityMapEntity;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.DateUtils;
import com.bto.commons.utils.TreeUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.convert.CityMapConvert;
import com.bto.plant.dao.CityMapDao;
import com.bto.plant.dao.PlantEvaluationMapper;
import com.bto.plant.dao.PlantMapper;
import com.bto.plant.dao.RadiantQuantityDao;
import com.bto.plant.service.*;
import com.bto.redis.utils.RedisUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.bto.commons.constant.RedisKey.*;

/**
 * 电站评估服务实现类
 * 
 * 电站评估相关业务服务的具体实现类，提供电站发电量预测、实际发电量对比、辐射量分析等评估功能。
 *
 * <AUTHOR>
 * @since 2024/1/5 9:42
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlantEvaluationServiceImpl implements PlantEvaluationService {
    public final PlantEvaluationMapper plantEvaluationMapper;
    public final GlobalParamUtil globalParamUtil;
    public final PlantService plantService;
    public final PlantMapper plantMapper;
    public final WeatherStationService weatherStationService;
    public final RadiantQuantityService radiantQuantityService;
    public final RadiantQuantityDao radiantQuantityDao;
    public final RedisUtil redisUtil;
    public final ForecastServiceImpl forecastServiceImpl;
    public final SystemServiceClient systemServiceClient;
    public final CityMapService cityMapService;
    public final CityMapDao cityMapDao;


    /**
     * 获取电站评估统计信息
     * 
     * 根据查询条件获取电站评估的统计信息，包括电站总数、总装机容量等关键指标。
     *
     * @param query 查询条件（包含项目ID等）
     * @return 电站评估统计信息视图对象
     */
    @Override
    public PlantEvaluationStaticsVO statics(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();

        PlantEvaluationStaticsVO vo = plantEvaluationMapper.statics(userInfo, query, null);

        PlantVO plantVO = new PlantVO();
        plantVO.setPlantTypeId(null);
        plantVO.setProjectSpecial(query.getProjectId());
        Integer plantTotal = plantMapper.getPlantCount(plantVO, userInfo);
        Integer plantCapacity = plantMapper.getPlantCapacityByProjectId(userInfo, query);

        BigDecimal bgPlantCapacity = new BigDecimal(plantCapacity);
        bgPlantCapacity = bgPlantCapacity.setScale(2, RoundingMode.HALF_UP);
        // String realPlantCapacity = BusinessCalculateUtil.getRealPlantCapacity(plantCapacity.toString());
        vo.setPlantTotal(plantTotal);
        vo.setPlantCapacity(bgPlantCapacity.toString());
        return vo;
    }

    /**
     * 获取辐射量数据（按日期）
     * 
     * 根据查询条件获取指定时间范围内的辐射量数据，按日期维度进行统计。
     *
     * @param query 查询条件（包含项目ID、时间范围等）
     * @return 辐射量数据列表（按日期分组）
     */
    @Override
    public List<Map<String, String>> getRadiationWithDate(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<String> stationUidList = getStationUidList(query);
        String redisKey = RADIATION + userInfo.getUserUid() + query.getProjectId() + query.getStartTime() + query.getEndTime();

        return radiantQuantityService.getRadiateByThirtyDays(stationUidList, redisKey);
    }

    /**
     * 获取气象站UID列表
     * 
     * 根据查询条件获取相关的气象站UID列表，用于辐射量数据查询。
     *
     * @param query 查询条件
     * @return 气象站UID列表
     */
    private List<String> getStationUidList(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<String> plantCities = plantMapper.getPlantCities(userInfo, query);
        List<String> stationUidList = weatherStationService.getStationUidListByCities(plantCities);
        return stationUidList;
    }

    /**
     * 获取基于辐射量的月度预测发电量
     * 
     * 根据辐射量数据计算月度预测发电量，支持Redis缓存，通过辐射量与实际发电量的关系模型进行预测计算。
     *
     * @param query 查询条件（包含项目ID、时间范围等）
     * @return 月度预测发电量数据（按日期分组）
     */
    @Override
    public Map<String, String> getPreElectricityByRadiationWithMonth(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();

        String today = DateUtil.today();
        String redisKey = PREDICTION_ELECTRICITY_MONTH + userInfo.getUserUid() + query.getProjectId() + REDIS_SPLIT + today;

        // 尝试从Redis中获取预测发电量
        Object obj = redisUtil.get(redisKey);
        // 如果存在，直接返回
        if (obj != null) {
            return (Map<String, String>) obj;
        }

        List<String> plantCities = plantMapper.getPlantCities(userInfo, query);
        Map<String, Map<Date, BigDecimal>> map = radiantQuantityDao.getRadiationByDateMonth(new RadiantQuantityQuery(plantCities, query.getStartTime(), query.getEndTime()));

        Map<String, String> radiationMap = new HashMap<>();
        for (Map.Entry<String, Map<Date, BigDecimal>> entry : map.entrySet()) {
            String collectDate = entry.getKey();
            String totalRadiantQuantity = entry.getValue().get("totalRadiantQuantity").toString();
            String predictionElectricity = radiantQuantityService.getPredictionElectricityByRadiation(totalRadiantQuantity, query.getProjectId());
            String realElectricity = BusinessCalculateUtil.getRealElectricity(predictionElectricity);
            radiationMap.put(collectDate, realElectricity);
        }

        TreeMap<String, String> result = new TreeMap<>(radiationMap);
        // 将结果存储到Redis中，并设置过期时间（假设设置为一天）
        redisUtil.set(redisKey, result, DateUtils.getExpTimeByEndOfToday());
        return result;
    }

    /**
     * 获取实际发电量
     * 
     * <p>根据查询条件获取实际发电量数据，用于发电量对比分析。</p>
     *
     * @param query 查询条件（包含项目ID等）
     * @return 实际发电量数据
     */
    @Override
    public BigDecimal getRealElectricity(PlantEvaluationQuery query) {
        getPlantUidList(globalParamUtil.getUserInfo(), query.getProjectId());
        return null;
    }

    /**
     * 获取电站UID列表
     * 
     * <p>根据用户信息获取指定项目下的所有电站UID列表。</p>
     *
     * @param userInfo 用户信息
     * @param projectId 项目ID
     * @return 电站UID列表
     */
    public List<String> getPlantUidList(RequireParamsDTO userInfo, String projectId) {
        List<PlantIdWithNameVO> list = plantMapper.selectAll(userInfo, projectId,null);
        return list.stream().map(PlantIdWithNameVO::getPlantUid).collect(Collectors.toList());
    }

    /**
     * 获取实际发电量数据（按日期）
     *
     * 根据查询条件获取指定时间范围内的实际发电量数据，按日期维度进行统计
     *
     * @param query 查询条件
     * @return 实际发电量数据列表
     */
    @Override
    public List<Map<String, String>> getElectricityWithDate(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<String> plantUidList = getPlantUidList(userInfo, query.getProjectId());
        String redisKey = ELECTRICITY + userInfo.getUserUid() + query.getProjectId() + query.getStartTime() + query.getEndTime();

        return plantService.getElectricityByDate(plantUidList, query.getStartTime(), query.getEndTime(), redisKey);
    }

    /**
     * 获取三个月发电量对比数据
     *
     * 获取最近三个月的发电量数据进行对比分析
     *
     * @param query 查询条件
     * @return 三个月发电量对比数据
     */
    @Override
    public Map<String, String> getElectricityThreeMonthsCompare(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String today = DateUtil.today();
        String redisKey = REALLY_ELECTRICITY_MONTH + userInfo.getUserUid() + query.getProjectId() + REDIS_SPLIT + today;

        // 尝试从Redis中获取预测发电量
        Object obj = redisUtil.get(redisKey);
        // 如果存在，直接返回
        if (obj != null) {
            return (Map<String, String>) obj;
        }

        List<String> plantUidList = getPlantUidList(userInfo, query.getProjectId());
        // 按月份：实际发电量
        return plantService.getElectricityByMonth(plantUidList, query.getStartTime(), query.getEndTime(), redisKey);

    }

    /**
     * 获取电站评估信息
     *
     * 获取电站评估的综合信息，包括实际效率和预测效率等关键指标
     *
     * @param query 查询条件
     * @return 电站评估信息
     */
    @Override
    public Map<String, String> getPlantEvaluationInfo(PlantEvaluationQuery query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        Integer capacity = plantMapper.getPlantCapacityByProjectId(userInfo, query);
        String realPlantCapacity = BusinessCalculateUtil.getRealPlantCapacity(Optional.ofNullable(capacity).orElse(0).toString());

        // 实发
        Map<String, String> really = this.getElectricityThreeMonthsCompare(query);
        // 预发
        Map<String, String> prediction = this.getPreElectricityByRadiationWithMonth(query);
        // Calculate the sum of values
        Map<String, BigDecimal> reallySum = calculateSum(really, "really");
        Map<String, BigDecimal> predictionSum = calculateSum(prediction, "prediction");

        String realEfficiencyPerHours = BusinessCalculateUtil.getRealEfficiencyPerHours(reallySum.get("really").toString(), realPlantCapacity);
        String predictionEfficiencyPerHours = BusinessCalculateUtil.getRealEfficiencyPerHours(predictionSum.get("prediction").toString(), realPlantCapacity);

        Map<String, String> map = new HashMap<>();
        map.put("really", realEfficiencyPerHours);
        map.put("prediction", predictionEfficiencyPerHours);
        return map;
    }


    /**
     * 计算发电量总和
     *
     * 根据发电量数据计算总和
     *
     * @param inputMap 发电量数据映射
     * @param key 发电量键值
     * @return 发电量总和映射
     */
    private static Map<String, BigDecimal> calculateSum(Map<String, String> inputMap, String key) {
        Map<String, BigDecimal> sumMap = new HashMap<>();

        for (Map.Entry<String, String> entry : inputMap.entrySet()) {
            String valueStr = entry.getValue();
            try {
                BigDecimal value = new BigDecimal(valueStr);
                sumMap.put(key, sumMap.getOrDefault(key, BigDecimal.ZERO).add(value));
            } catch (NumberFormatException e) {
                throw new BusinessException(e.toString());
            }
        }

        return sumMap;
    }

    /**
     * 获取气象站项目树形结构
     *
     * 获取用于气象站展示的项目树形结构数据
     *
     * @return 项目树形结构列表
     */
    @Override
    public List<ProjectInfoVO> getProjectTreeForWeatherStation() {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        List<String> cities = weatherStationService.getAllCity();

        List<ProjectInfoVO> list = plantMapper.getProjectListByCities(userInfo, cities);
        return TreeUtils.build(list);
    }

    /**
     * 获取区域电站树形结构
     *
     * 根据项目ID获取区域电站的树形结构数据，支持地图展示
     *
     * @param projectId 项目ID
     * @return 区域电站树形结构列表
     */
    @Override
    public List<CityMapVO> getCityAreaTreeWithMapId(String projectId) {
        List<String> cities = plantService.getCitiesByPid(projectId);
        List<String> areas = weatherStationService.getAreaByCities(cities);
        if (CollUtil.isNotEmpty(areas)) {
            cities.addAll(areas);
        }
        // List<CityMapVO> list = cityMapDao.getParentAndChild(areas);

        LambdaQueryWrapper<CityMapEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CityMapEntity::getName, cities);
        List<CityMapEntity> list = cityMapService.list(wrapper);

        return TreeUtils.build(CityMapConvert.INSTANCE.convertList(list));
    }


}
