package com.bto.plant.service.impl;

import com.bto.commons.pojo.entity.WeatherRadiationEntity;
import com.bto.commons.pojo.vo.DateRadiationVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.commons.utils.DateUtils;
import com.bto.plant.dao.WeatherRadiationMapper;
import com.bto.plant.service.WeatherRadiationService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * 天气辐射量服务实现类
 *
 * 天气与辐射量关系相关业务服务的具体实现类，提供基于天气状况的辐射量查询功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-06
 */
@Service
@AllArgsConstructor
public class WeatherRadiationServiceImpl extends BaseServiceImpl<WeatherRadiationMapper, WeatherRadiationEntity> implements WeatherRadiationService {

    private final WeatherRadiationMapper weatherRadiationMapper;

    /**
     * 根据天气状况获取辐射量
     * 
     * @param weather 天气状况描述
     * @return 对应天气状况的辐射量数值
     */ 
    @Override
    public Double getRadiationByWeather(String weather) {
        return weatherRadiationMapper.getRadiationByWeather(weather);
    }

    /**
     * 获取历史天气辐射数据
     * 
     * @param localDate 查询日期
     * @param city 城市名称
     * @return 指定日期和城市的天气辐射数据
     */ 
    @Override
    public DateRadiationVO getHistoryRadiationByWeather(LocalDate localDate, String city) {
        String tableName = "bto_weather_" + DateUtils.getCurrentMonth(localDate);
        return weatherRadiationMapper.getHistoryRadiationByWeather(tableName, localDate, city);
    }
}