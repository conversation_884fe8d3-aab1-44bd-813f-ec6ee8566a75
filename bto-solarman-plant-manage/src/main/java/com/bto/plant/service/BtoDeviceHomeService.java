package com.bto.plant.service;

import com.bto.commons.pojo.dto.BtoDeviceHomeQuery;
import com.bto.commons.pojo.entity.BtoDeviceHomeEntity;
import com.bto.commons.pojo.vo.BtoDeviceHomeVO;
import com.bto.commons.service.BaseService;

import java.util.List;

/**
 * 家居设备服务接口
 * 
 * 提供家居设备相关的业务服务，包括设备信息查询、增删改查等操作
 *
 * <AUTHOR>
 * @since 1.0.0 2025-03-26
 */
public interface BtoDeviceHomeService extends BaseService<BtoDeviceHomeEntity> {

    /**
     * 获取家居设备列表
     * 
     * 根据查询条件获取家居设备信息列表
     *
     * @param query 设备查询条件对象，包含过滤和分页参数
     * @return List<BtoDeviceHomeVO> 设备信息视图对象列表
     */
    List<BtoDeviceHomeVO> getList(BtoDeviceHomeQuery query);

    /**
     * 处理保存或更新操作
     * 
     * 根据设备信息视图对象保存新设备或更新现有设备信息
     *
     * @param vo 设备信息视图对象，包含设备的所有相关信息
     */
    void handleSaveOrUpdate(BtoDeviceHomeVO vo);

    /**
     * 删除设备
     * 
     * 根据查询条件删除指定的家居设备
     *
     * @param query 设备查询条件对象，指定要删除的设备
     */
    void delete(BtoDeviceHomeQuery query);

    /**
     * 获取设备详细信息
     * 
     * 根据设备ID和用户UID获取指定设备的详细信息
     *
     * @param deviceId 设备唯一标识符
     * @param userUid 用户唯一标识符，用于权限验证
     * @return BtoDeviceHomeVO 设备详细信息视图对象
     */
    BtoDeviceHomeVO getDeviceInfo(String deviceId, String userUid);
}