package com.bto.plant.service;

import com.alibaba.fastjson.JSONObject;
import com.bto.commons.pojo.dto.PassiveModeDTO;
import com.bto.commons.pojo.dto.PassivePowerDTO;
import com.bto.commons.pojo.dto.SOCRetainDTO;
import com.bto.commons.pojo.dto.WorkModeConfigDTO;
import com.bto.commons.pojo.entity.BtoSetParameter;

/**
 * 工作模式服务接口
 * 
 * 提供设备工作模式相关的业务接口
 * 主要用于配置和管理设备的工作模式参数
 * 
 * <AUTHOR>
 * @date 2025/4/16
 */
public interface WorkModeService {

    /**
     * 获取工作模式参数
     * 
     * 根据设备序列号获取设备的工作模式参数配置
     * 
     * @param deviceSn 设备序列号
     * @return 工作模式参数配置
     */    
    BtoSetParameter getWorkModeParam(String deviceSn);

    /**
     * 工作模式时间设置
     * 
     * 设置设备的工作模式时间参数
     * 
     * @param workModeConfigDTO 工作模式配置DTO
     * @return JSON响应对象
     */
    JSONObject workModeTimeSet(WorkModeConfigDTO workModeConfigDTO);

    /**
     * 设置SOC保留值
     * 
     * 设置设备的SOC（State of Charge）保留值
     * 
     * @param socRetainDTO SOC保留DTO
     * @return JSON响应对象
     */
    JSONObject backModeSOCSet(SOCRetainDTO socRetainDTO);

    /**
     * 设置被动模式
     * 
     * 设置设备的被动工作模式参数
     * 
     * @param passiveModeDTO 被动模式DTO
     * @return JSON响应对象
     */
    JSONObject passiveModeSet(PassiveModeDTO passiveModeDTO);

    /**
     * 设置被动功率
     * 
     * 设置设备的被动功率参数
     * 
     * @param passivePowerDTO 被动功率DTO
     * @return JSON响应对象
     */
    JSONObject passivePowerSet(PassivePowerDTO passivePowerDTO);
}
