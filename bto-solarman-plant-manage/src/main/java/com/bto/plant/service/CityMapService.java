package com.bto.plant.service;

import com.bto.commons.pojo.entity.CityMapEntity;
import com.bto.commons.pojo.vo.CityMapVO;
import com.bto.commons.service.BaseService;

import java.util.List;

/**
 * 城市地图服务接口
 * 
 * 提供城市地图相关的业务服务，包括城市地图数据管理、树形结构查询、增删改查等功能
 *
 * <AUTHOR> 
 * @since  2024-04-17
 */
public interface CityMapService extends BaseService<CityMapEntity> {

    /**
     * 保存城市地图信息
     * 
     * 保存新的城市地图信息到数据库
     *
     * @param vo 城市地图信息视图对象，包含需要保存的地图信息
     */
    void save(CityMapVO vo);

    /**
     * 更新城市地图信息
     * 
     * 更新已存在的城市地图信息
     *
     * @param vo 城市地图信息视图对象，包含需要更新的地图信息
     */
    void update(CityMapVO vo);

    /**
     * 删除城市地图信息
     * 
     * 根据ID列表批量删除城市地图信息
     *
     * @param idList 城市地图ID列表，指定需要删除的地图记录
     */
    void delete(List<Long> idList);

    /**
     * 获取城市地图树形结构
     * 
     * 获取城市地图的树形结构数据，用于层级展示
     *
     * @return List<CityMapVO> 城市地图信息视图对象列表，以树形结构返回
     */
    List<CityMapVO> getTree();
}