package com.bto.plant.service;


import com.bto.commons.pojo.dto.BtoUserRoomQuery;
import com.bto.commons.pojo.entity.BtoUserRoomEntity;
import com.bto.commons.pojo.vo.BtoUserRoomVO;
import com.bto.commons.service.BaseService;

import java.util.List;

/**
 * 空间管理服务接口
 * 
 * 提供用户空间管理相关的业务服务，包括用户房间管理、空间信息查询、增删改查等功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-03-26
 */
public interface BtoUserRoomService extends BaseService<BtoUserRoomEntity> {

    /**
     * 获取用户空间列表
     * 
     * 根据用户唯一标识符获取该用户的所有空间信息列表
     *
     * @param userUid 用户唯一标识符
     * @return List<BtoUserRoomVO> 用户空间信息视图对象列表
     */
    List<BtoUserRoomVO> list(String userUid);

    /**
     * 处理保存或更新操作
     * 
     * 根据用户空间信息视图对象保存新空间或更新现有空间信息
     *
     * @param vo 用户空间信息视图对象，包含空间的所有相关信息
     */
    void handleSaveOrUpdate(BtoUserRoomVO vo);

    /**
     * 删除空间
     * 
     * 根据查询条件删除指定的用户空间
     *
     * @param query 用户空间查询条件对象，指定要删除的空间
     */
    void delete(BtoUserRoomQuery query);
}