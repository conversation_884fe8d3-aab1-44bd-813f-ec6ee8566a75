package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.api.feign.alarm.AlarmServiceClient;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.constant.*;
import com.bto.commons.converter.vo.PlantVOMapper;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.PlantQueryDTO;
import com.bto.commons.pojo.dto.PlantUpdateDTO;
import com.bto.commons.pojo.dto.PowerPlantInfoQueryDTO;
import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.vo.BatteryDataVO;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.entity.PlantType;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.CreateExcelUtils;
import com.bto.commons.utils.DateUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.PlantMapper;
import com.bto.plant.dao.PlantTypeMapper;
import com.bto.plant.dao.UserCollectPlantMapper;
import com.bto.plant.service.ForecastService;
import com.bto.plant.service.PlantService;
import com.bto.plant.service.WeatherStationService;
import com.bto.redis.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 电站服务实现类
 *
 * 电站相关业务服务的具体实现类，提供电站管理、查询、统计、导出等核心功能。
 * 该类整合了设备管理、天气数据、告警服务等外部服务，实现电站全生命周期管理。
 *
 * <AUTHOR>
 * @date 2023/3/30 17:35
 */
@Service(value = "plantService")
@Slf4j
public class PlantServiceImpl extends ServiceImpl<PlantMapper, Plant> implements PlantService {
    @Autowired
    private GlobalParamUtil globalParamUtil;

    @Autowired
    private ForecastService forecastService;

    @Autowired
    private PlantMapper plantMapper;

    @Autowired
    private PlantVOMapper plantVOMapper;

    @Autowired
    private DeviceServiceClient deviceServiceClient;

    @Autowired
    private SystemServiceClient systemServiceClient;
    @Autowired
    private AlarmServiceClient alarmServiceClient;

    @Autowired
    private PlantTypeMapper plantTypeMapper;

    @Autowired
    private WeatherStationService weatherStationService;

    @Autowired
    private UserCollectPlantMapper userCollectPlantMapper;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取电站分页列表
     *
     * 根据查询条件获取电站的分页列表数据，支持排序和权限过滤
     *
     * @param plantVO 电站查询条件
     * @param startPage 起始页码
     * @param pageSize 每页大小
     * @param sortType 排序类型
     * @param userInfo 用户信息（包含权限信息）
     * @return 电站分页数据
     * @throws BusinessException 当排序类型为空时抛出异常
     */ 
    @Override
    public Page<com.bto.commons.pojo.dto.PlantVO> getPlants(com.bto.commons.pojo.dto.PlantVO plantVO, String startPage, String pageSize, String sortType, RequireParamsDTO userInfo) {
        String sortTypeName = SortType.getNameByCode(sortType);
        String[] sortTypeArr;
        if (SortType.NONE.getName().equals(sortTypeName)) {
            throw new BusinessException(ResultEnum.REQUIREDPARAM_EMPTY);
        } else {
            sortTypeArr = sortTypeName.split(";");
        }
        Page<com.bto.commons.pojo.dto.PlantVO> plantPage = new Page<com.bto.commons.pojo.dto.PlantVO>(Integer.parseInt(startPage), Integer.parseInt(pageSize));
        List<com.bto.commons.pojo.dto.PlantVO> plantVOS = plantMapper.getPlants(plantVO, plantPage, sortTypeArr[0], sortTypeArr[1], userInfo);
        Integer plantCount = plantMapper.getPlantCount(plantVO, userInfo);
        plantPage.setRecords(plantVOS);
        plantPage.setTotal(plantCount);
        return plantPage;
    }

    /**
     * 获取电站树形结构数据
     *
     * 根据电站名称模糊查询获取电站的树形结构数据，包含电站ID和名称
     *
     * @param plantName 电站名称（支持模糊查询）
     * @param startPage 起始页码
     * @param pageSize 每页大小
     * @param userInfo 用户信息（包含权限信息）
     * @return 电站分页数据（仅包含ID和名称）
     * @throws BusinessException 当用户权限无效时抛出异常
     */ 
    @Override
    public Page<Plant> getPlantTree(String plantName, String startPage, String pageSize, RequireParamsDTO userInfo) {
        Page<Plant> plantPage = new Page<Plant>(Integer.parseInt(startPage), Integer.parseInt(pageSize));
        QueryWrapper<Plant> plantQueryWrapper = new QueryWrapper<>();
        plantQueryWrapper.like("plant_name", plantName);
        plantQueryWrapper.select("plant_uid", "plant_name");
        plantQueryWrapper.eq("is_deleted", "0");
        if (userInfo.getPlantList() != null && userInfo.getPlantList().size() > 0) {
            plantQueryWrapper.in("plant_uid", userInfo.getPlantList());
        } else if (userInfo.getProjectList() != null && userInfo.getProjectList().size() > 0) {
            plantQueryWrapper.in("project_special", userInfo.getProjectList());
        } else {
            throw new BusinessException(ResultEnum.USER_ACCOUNT_INVALIDATED);
        }

        plantQueryWrapper.orderByAsc("plant_name");
        plantMapper.selectPage(plantPage, plantQueryWrapper);
        return plantPage;
    }

    /**
     * 获取电站详细信息
     *
     * 根据电站ID获取电站的详细信息，包括基础信息、计算字段、用户信息和设备信息
     * 会对数值进行单位换算和格式化计算
     *
     * @param plantUid 电站唯一标识
     * @return 电站详细信息视图对象，如果电站不存在返回null
     */ 
    @Override
    public com.bto.commons.pojo.dto.PlantVO getPlantInfo(String plantUid) {
        QueryWrapper<Plant> plantQueryWrapper = new QueryWrapper<>();
        plantQueryWrapper.eq("plant_uid", plantUid);
        plantQueryWrapper.eq("is_deleted", "0");
        Plant plant = plantMapper.selectOne(plantQueryWrapper);
        com.bto.commons.pojo.dto.PlantVO plantVO = plantVOMapper.plant2PlantVO(plant);
        if (plantVO != null) {
            // todo 对power capacity todayelectricity 字段添加条件判断
            BigDecimal power = new BigDecimal(plant.getPower());
            BigDecimal plantCapacity = new BigDecimal(plant.getPlantCapacity());
            BigDecimal todayElectricity = new BigDecimal(plant.getTodayElectricity());
            BigDecimal totalElectricity = new BigDecimal(plant.getTotalElectricity());
            BigDecimal salePrice = new BigDecimal(plant.getSalePrice());
            String createTime = plant.getCreateTime();
            // 计算质保日期
            plantVO.setWarrantyTime(DateUtils.getDateOffsetByYear(createTime, Constant.WARRANTY_TERM));
            // 计算装机容量
            plantVO.setPlantCapacity(String.valueOf(plantCapacity.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_HALF_UP)));
            // 计算工作效率
            plantVO.setEfficiency(power.divide(new BigDecimal("1000"), 2, BigDecimal.ROUND_HALF_UP).divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100")).toString());
            // 计算累计等效植树
            plantVO.setEquivalentTreePlanting(String.valueOf(BusinessCalculateUtil.calculate(String.valueOf(totalElectricity)).getEquivalentTrees()));
            // 计算累计减排二氧化碳
            plantVO.setCarbonDioxide(String.valueOf(BusinessCalculateUtil.calculate(String.valueOf(totalElectricity)).getCo2Reduction()));
            // 计算今日发电量
            plantVO.setTodayElectricity(new BigDecimal(plantVO.getTodayElectricity()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString());
            // 计算当月发电量
            plantVO.setMonthElectricity(new BigDecimal(plantVO.getMonthElectricity()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString());
            // 计算当年发电量
            plantVO.setYearElectricity(new BigDecimal(plantVO.getYearElectricity()).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString());
            // 计算当日收益
            plantVO.setTodayEarning(todayElectricity.multiply(salePrice).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString());
            // 计算累计收益
            plantVO.setTotalEarning(totalElectricity.multiply(salePrice).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString());
            plantVO.setTotalElectricity(totalElectricity.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toString());
            // 使用PlantStatus枚举类转换 电站plantStatus属性值
            plantVO.setPlantStatus(PlantStatusEnum.getNameByCode(plantVO.getPlantStatus()));
            // 使用Orientation枚举类转换 电站orientation属性值
            plantVO.setOrientation(OrientationEnum.getNameByCode(plantVO.getOrientation()));
            // 查询电站的用户电话名称
            Result userNameAndPhone = systemServiceClient.getUserNameAndPhoneByUserUid(plant.getUserUid());
            if (ResultEnum.SUCCESS.getCode().equals(userNameAndPhone.getStatus())) {
                Map<String, String> userNameAndPhoneMap = (Map<String, String>) userNameAndPhone.getData();
                plantVO.setUserName(userNameAndPhoneMap.get("userName"));
                plantVO.setUserPhone(userNameAndPhoneMap.get("userPhone"));
            } else if (ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode().equals(userNameAndPhone.getStatus())) {
                throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD);
            }

            Result peakPowerByPlantUid = deviceServiceClient.getTodayPeakPowerByPlantUid(plantVO.getPlantUid());
            if (ResultEnum.SUCCESS.getCode().equals(peakPowerByPlantUid.getStatus())) {
                String peakPower = (String) peakPowerByPlantUid.getData();
                plantVO.setPeakPower(peakPower);
            } else if (ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode().equals(peakPowerByPlantUid.getStatus())) {
                throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD);
            } else {
                plantVO.setPeakPower("0");
            }
            // 获取在线逆变器数量
            Result onlineInverterStats = deviceServiceClient.getOnlineInverterStats(plantUid);
            if (ResultEnum.SUCCESS.getCode().equals(onlineInverterStats.getStatus())) {
                plantVO.setOnlineInverterStats(String.valueOf(onlineInverterStats.getData()));
            } else if (ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode().equals(onlineInverterStats.getStatus())) {
                throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD);
            }
            // 查询电站下所有的逆变器id，填装到plantvo视图层对象
            Result devicesId = deviceServiceClient.getDevicesId(plantUid);
            if (ResultEnum.SUCCESS.getCode().equals(devicesId.getStatus())) {
                plantVO.setInverterSN((List<String>) devicesId.getData());

            } else if (ResultEnum.FEIGNCLINET_REQUESTEST_FAILD.getCode().equals(devicesId.getStatus())) {
                throw new BusinessException(ResultEnum.FEIGNCLINET_REQUESTEST_FAILD);
            } else {
                plantVO.setInverterSN(Arrays.asList("电站没有查询到逆变器".split(",")));
            }
            return plantVO;
        }
        return null;
    }

    /**
     * 根据电站ID获取电站名称
     * 
     * @param plantUid 电站唯一标识
     * @return 电站名称，如果电站不存在或名称为空返回null
     */ 
    @Override
    public String getPlantNameByPlantUid(String plantUid) {
        QueryWrapper<Plant> plantQueryWrapper = new QueryWrapper<>();
        plantQueryWrapper.eq("plant_uid", plantUid);
        Plant plant = plantMapper.selectOne(plantQueryWrapper);
        if (plant != null && plant.getPlantName() != null) {
            return plant.getPlantName();
        }
        return null;
    }

    /**
     * 按月统计电站发电量
     * 
     * @param plantUid 电站唯一标识
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 月度发电量统计数据列表
     */ 
    @Override
    public List<HashMap<String, String>> getPlantElectricityStatsByMonth(String plantUid, String startTime, String endTime) {
        List<HashMap<String, Object>> plantElectricityStatsByMonthList = plantMapper.getPlantElectricityStatsByMonth(plantUid, startTime, endTime);
        List<HashMap<String, String>> returnPlantElectricityStatsByMonthList = new ArrayList<>();
        HashMap<String, String> returnPlantElectricityStatsByMonth = null;
        if (plantElectricityStatsByMonthList.size() > 0) {
            for (HashMap<String, Object> plantElectricityStatsByMonth : plantElectricityStatsByMonthList) {
                returnPlantElectricityStatsByMonth = new HashMap<>();
//                BigDecimal powerOrElectricity = new BigDecimal(String.valueOf(plantElectricityStatsByMonth.get("powerOrElectricity"))).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal powerOrElectricity = new BigDecimal(String.valueOf(plantElectricityStatsByMonth.get("powerOrElectricity")));
                returnPlantElectricityStatsByMonth.put("powerOrElectricity", powerOrElectricity.toString());
                String dataTime = DateUtil.formatDate(DateUtil.parse(String.valueOf(plantElectricityStatsByMonth.get("dataTime"))));
                returnPlantElectricityStatsByMonth.put("dataTime", dataTime);
                returnPlantElectricityStatsByMonthList.add(returnPlantElectricityStatsByMonth);
            }
        }
        return returnPlantElectricityStatsByMonthList;
    }

    /**
     * 按年统计电站发电量
     * 
     * @param plantUid 电站唯一标识
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 年度发电量统计数据列表
     */ 
    @Override
    public List<HashMap<String, BigDecimal>> getPlantElectricityStatsByYear(String plantUid, String startTime, String endTime) {
        List<HashMap<String, BigDecimal>> plantElectricityStatsByYearList = plantMapper.getPlantElectricityStatsByYear(plantUid, startTime, endTime);
        if (plantElectricityStatsByYearList.size() > 0) {
            for (HashMap<String, BigDecimal> plantElectricityStatsByYear : plantElectricityStatsByYearList
            ) {
                BigDecimal powerOrElectricity = plantElectricityStatsByYear.get("powerOrElectricity");
                plantElectricityStatsByYear.remove("powerOrElectricity");
                plantElectricityStatsByYear.put("powerOrElectricity", powerOrElectricity.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP));
            }
        }
        return plantElectricityStatsByYearList;
    }

    /**
     * 根据电站类型ID获取类型名称
     * 
     * @param plantTypeId 电站类型ID
     * @return 电站类型名称
     */ 
    @Override
    public String getPlantTypeNameByPlantTypeId(String plantTypeId) {
        QueryWrapper<PlantType> plantTypeQueryWrapper = new QueryWrapper<>();
        plantTypeQueryWrapper.eq("id", plantTypeId);
        PlantType plantType = plantTypeMapper.selectOne(plantTypeQueryWrapper);
        return plantType.getTypeName();
    }

    /**
     * 根据电站ID获取电站实体
     * 
     * @param plantUid 电站唯一标识
     * @return 电站实体对象
     */ 
    @Override
    public Plant getPlantInfoByPlantUid(String plantUid) {
        QueryWrapper<Plant> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("plant_uid", plantUid);
        Plant plant = plantMapper.selectOne(queryWrapper);
        return plant;
    }

    /**
     * 更新电站信息
     *
     * 根据电站ID更新电站的基础信息，包括电站名称、容量、地址等字段
     * 支持部分字段更新，只更新非空字段
     *
     * @param plantUpdateDTO 电站更新信息数据传输对象
     * @return 更新影响的行数
     */ 
    @Override
    public Integer updatePlantInfo(PlantUpdateDTO plantUpdateDTO) {
        LambdaUpdateWrapper<Plant> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Plant::getPlantUid, plantUpdateDTO.getPlantUid());

        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getMeterId()), Plant::getMeterId, plantUpdateDTO.getMeterId());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getPlantName()), Plant::getPlantName, plantUpdateDTO.getPlantName());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getPlantCapacity()), Plant::getPlantCapacity,
                new BigDecimal(plantUpdateDTO.getPlantCapacity()).multiply(BigDecimal.valueOf(1000)).toString());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getProvince()), Plant::getProvince, plantUpdateDTO.getProvince());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getCity()), Plant::getCity, plantUpdateDTO.getCity());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getArea()), Plant::getArea, plantUpdateDTO.getArea());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getAddress()), Plant::getAddress, plantUpdateDTO.getAddress());

        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getLatitude()), Plant::getLatitude, plantUpdateDTO.getLatitude());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getLongitude()), Plant::getLongitude, plantUpdateDTO.getLongitude());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getCreateTime()), Plant::getCreateTime, plantUpdateDTO.getCreateTime());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getSalePrice()), Plant::getSalePrice, plantUpdateDTO.getSalePrice());
        updateWrapper.set(StrUtil.isNotEmpty(plantUpdateDTO.getOrientation()), Plant::getOrientation, plantUpdateDTO.getOrientation());

        return plantMapper.update(null, updateWrapper);
    }

    /**
     * 根据电站编号查询电站详情信息
     *
     * @param plantUid 电站编号
     * @return 电站详情信息
     */
    @Override
    public PlantVO getPlantDetail(String plantUid) {
        String today = DateUtil.today();
        // 查询电站基础信息和用户信息
        PlantVO plantInfo = plantMapper.selectPlantInfo(plantUid);
        // 查询电站所属所有逆变器SN列表
        Result result = deviceServiceClient.getInverterSnList(plantUid);
        List<String> voltmeterIds = plantMapper.getAmmeterByPlantUid(plantUid);
        plantInfo.setVoltmeterIds(voltmeterIds);
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            String jsonString = JSON.toJSONString(result.getData());
            plantInfo.setInverterSN(JSONUtil.toList(jsonString, String.class));
        }
        // 如果该电站为储能
        if (PlantTypeEnum.ENERGY_STORAGE.getName().equals(plantInfo.getPlantTypeId())) {
            List<String> inverterSns = plantInfo.getInverterSN();
            if (CollUtil.isNotEmpty(inverterSns) && inverterSns.size() <= 1) {
                // 家储，单逆变器
                String inverterSn = inverterSns.get(0);
                String date = DateUtil.today();
                BatteryBaseVO btoBattery = plantMapper.getStoredEnergyBaseInfo(inverterSn, "bto_battery_" + date.replace("-", ""), date);
                // 获取最新功率汇总
                WorkPowerDTO powerSummary = plantMapper.getPowerSummary(inverterSn);
                plantInfo.setPower(powerSummary.getPvPower());
                btoBattery.setSystotalLoadwatt(Double.valueOf(powerSummary.getLoadPower()));
                btoBattery.setSysgridPowerwatt(Double.valueOf(powerSummary.getGridPower()));
                btoBattery.setStatus(powerSummary.getStatus());
                String batteryGroupDatalist = btoBattery.getBatteryGroupDatalist();
                JSONArray array = JSONUtil.parseArray(batteryGroupDatalist);
                List<BatteryDataVO> list = JSONUtil.toList(array, BatteryDataVO.class);
                list.get(0).setTotalPower(powerSummary.getBatPower().toString());

                if  (CollUtil.isNotEmpty(list)) {
                    plantInfo.setBatteryDataList(list);
                }
                plantInfo.setBatteryBaseVO(btoBattery);
            } else {
                // todo工商业
            }

        }
        // 如果该电站存在逆变器
        if (CollUtil.isNotEmpty(plantInfo.getInverterSN())) {
            // 查询逆变器当日告警数量
            Result inverterAlarmNum = alarmServiceClient.getInverterAlarmNum(today, plantInfo.getInverterSN());
            plantInfo.setTodayAlarmNum((Integer) inverterAlarmNum.getData());
            // 查询在线逆变器数量
            Result onlineInverterNum = deviceServiceClient.getOnlineInverterStats(plantUid);
            plantInfo.setOnlineInverterNum((String) onlineInverterNum.getData());
            // 查询峰值功率
            Result plantMaxPower = deviceServiceClient.getPlantMaxPower(today, plantInfo.getInverterSN());
            plantInfo.setMaxPower((String) plantMaxPower.getData());
        }
        // 是否存在结构温度检测
        List<Device> allDevices = plantMapper.hasStructureTemp(plantUid);
        List<String> structureList = allDevices.stream()
                .filter(d -> DeviceType.STRUCTURE.getCode().equals(String.valueOf(d.getDeviceType())))
                .map(Device::getDeviceId)
                .collect(Collectors.toList());
        String tempId = allDevices.stream()
                .filter(d -> DeviceType.TEMP.getCode().equals(String.valueOf(d.getDeviceType())))
                .map(Device::getDeviceId)
                .findFirst()
                .orElse(null);
        plantInfo.setStructureIds(structureList);
        plantInfo.setTempIds(tempId);

        String plantCapacity = plantInfo.getPlantCapacity();
        String totalElectricity = plantInfo.getTotalElectricity();
        // 计算工作效率
        plantInfo.setWorkEfficiency(BusinessCalculateUtil.getWorkEfficiency(plantInfo.getPower().toString(), plantCapacity));
        // 计算等效植树数量
        plantInfo.setTreeNum(BusinessCalculateUtil.getTreeNum(totalElectricity));
        // 计算Co2减排量
        plantInfo.setReduceCo2(BusinessCalculateUtil.getReduceCo2(totalElectricity));
        // 计算标准煤节约量
        plantInfo.setReduceCoal(BusinessCalculateUtil.getReduceCoal(totalElectricity));
        // 计算日、总 收益
        plantInfo.setTodayIncome(BusinessCalculateUtil.getIncome(plantInfo.getTodayElectricity(), plantInfo.getSalePrice()));
        plantInfo.setTotalIncome(BusinessCalculateUtil.getIncome(totalElectricity, plantInfo.getSalePrice()));
        // 计算日、年 等效利用小时
        plantInfo.setDailyEfficiencyPerHour(BusinessCalculateUtil.getEfficiencyPerHours(plantInfo.getTodayElectricity(), plantCapacity));
        plantInfo.setYearlyEfficiencyPerHour(BusinessCalculateUtil.getEfficiencyPerHours(totalElectricity, plantCapacity));
        // 计算质保日期
        plantInfo.setWarrantyTime(DateUtils.getDateOffsetByYear(plantInfo.getCreateTime(), Constant.WARRANTY_TERM));
        // 发电量单位换算
        plantInfo.setTodayElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getTodayElectricity()));
        plantInfo.setMonthElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getMonthElectricity()));
        plantInfo.setYearElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getYearElectricity()));
        plantInfo.setTotalElectricity(BusinessCalculateUtil.getRealElectricity(plantInfo.getTotalElectricity()));
        plantInfo.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(plantInfo.getPlantCapacity()));
        return plantInfo;
    }

    /**
     * 获取电站列表
     *
     * @param query 查询条件
     * @param userInfo 用户信息
     * @return 电站列表
     */
    @Override
    public PlantListPageVO<PlantInfoVO> getPlantList(PlantQueryDTO query, RequireParamsDTO userInfo) {
        if (DateUtils.compareDate(query.getStartCreateTime(), query.getEndCreateTime())) {
            throw new BusinessException(ResultEnum.DATETIME_CHECK_FAILED);
        }
        Page<PlantInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        if (ObjectUtil.isEmpty(query.getOrder())) {
            query.setOrder("create_time");
        }
        IPage<PlantInfoVO> plantList = plantMapper.getPlantList(query, userInfo, page);
        PlantListCountResultVO plantResultCountVO = plantMapper.getPlantListResult(query, userInfo);
        return new PlantListPageVO<>(plantList, plantResultCountVO);
    }

    /**
     * 工程师获取电站列表
     *
     * @param query 查询条件
     * @return 电站列表
     */
    @Override
    public PlantListPageVO<Plant> engineerGetPlantList(PlantQueryDTO query) {
        IPage<Plant> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        if (ObjectUtil.isEmpty(query.getOrder())) {
            query.setOrder("create_time");
        }
        LambdaQueryWrapper<Plant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(Plant::getPlantName, query.getPlantName());
        page = this.page(page, queryWrapper);
        return new PlantListPageVO<>(page, null);
    }

    /**
     * 获取所有电站ID和名称列表
     * 
     * @param userInfo 用户信息（包含权限信息）
     * @param plantName 电站名称（可选，支持模糊查询）
     * @return 电站ID和名称列表
     */ 
    @Override
    public List<PlantIdWithNameVO> selectAll(RequireParamsDTO userInfo, String plantName) {
        return plantMapper.selectAll(userInfo, null, plantName);
    }

    /**
     * 根据城市获取电站ID列表
     * 
     * @param city 城市名称
     * @return 该城市下的所有电站ID列表
     */ 
    @Override
    public List<String> getPlantIdByCity(String city) {
        LambdaQueryWrapper<Plant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Plant::getCity, city);
        List<Plant> plants = plantMapper.selectList(wrapper);
        return plants.stream().map(Plant::getPlantUid).collect(Collectors.toList());
    }

    /**
     * 通过城市获取电站发电量数据
     *
     * @param city 城市
     * @return 发电量数据
     */
    @Override
    public List<BatteryDivinerVO> getElectricityByCity(String city) {
        return plantMapper.getElectricityByCity(city);
    }

    /**
     * 获取站内电站分页数据
     *
     * @param userInfo 用户信息
     * @param query 电站查询条件
     * @return 电站分页数据
     */
    @Override
    public IPage<PlantBaseInfoVO> getInStationPlantPage(RequireParamsDTO userInfo, PlantQueryDTO query) {
        Page<PlantInfoVO> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        if (ObjectUtil.isEmpty(query.getOrder())) {
            query.setOrder("create_time");
        }
        // 查询气象站城市
        List<String> allCity = weatherStationService.getAllCity();
        query.setStationPlantList(allCity);
        return plantMapper.getInStationPlantPage(query, userInfo, page);
    }

    /**
     * 收藏电站接口
     *
     * @param userUid 用户唯一标识符
     * @param plantUid 电站唯一标识符
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer collectPlant(String userUid, String plantUid) {
        String collectTime = DateUtil.now();
        try {
            return plantMapper.collectPlant(userUid, plantUid, collectTime);
        } catch (DuplicateKeyException e) {
            throw new BusinessException(ResultEnum.DATA_EXISTED_ERROR);
        }
    }

    /**
     * 通过电站名称获取电站信息
     *
     * @param plantName 电站名称
     * @return 电站信息
     */
    @Override
    public Plant getPlantInfoByPlantName(String plantName) {
        LambdaQueryWrapper<Plant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Plant::getPlantName, plantName);
        return plantMapper.selectOne(wrapper);
    }


    /**
     * 获取电站发电量数据
     *
     * @param plantUidList 电站id列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param redisKey redis键
     * @return 发电量数据
     */
    @Override
    public List<Map<String, String>> getElectricityByDate(List<String> plantUidList, String startTime, String endTime, String redisKey) {
        // 尝试从Redis中获取预测发电量
        Object obj = redisUtil.get(redisKey);
        if (obj != null) {
            return (List<Map<String, String>>) obj;
        }
        List<Map<String, String>> result = plantMapper.getElectricityByDate(plantUidList, startTime, endTime);
        redisUtil.set(redisKey, result, DateUtils.getExpTimeByEndOfToday());

        return result;
    }

    /**
     * 获取电站发电量数据
     *
     * @param plantUidList 电站id列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param redisKey redis键
     * @return 发电量数据
     */
    @Override
    public Map<String, String> getElectricityByMonth(List<String> plantUidList, String startTime, String endTime, String redisKey) {
        HashMap<String, LinkedHashMap<String, BigDecimal>> map = plantMapper.getElectricityByMonth(plantUidList, startTime, endTime);

        Map<String, String> electricityMap = new HashMap<>();

        for (Map.Entry<String, LinkedHashMap<String, BigDecimal>> entry : map.entrySet()) {
            String totalElectricity = entry.getValue().get("totalElectricity").toString();
            String collectDate = entry.getKey();
            electricityMap.put(collectDate, totalElectricity);
        }
        TreeMap<String, String> result = new TreeMap<>(electricityMap);
        redisUtil.set(redisKey, result, DateUtils.getExpTimeByEndOfToday());
        return result;
    }

    /**
     * 根据地理位置获取电站信息（分页）
     *
     * @param query 查询参数
     * @return 电站信息分页数据
     */
    @Override
    public IPage<PowerPlantInfoVO> getPowerPlantInfoByLocation(PowerPlantInfoQueryDTO query) {
        List<LocalDate> dateList = null;

        if (query.getStartCreateTime() != null && query.getEndCreateTime() != null) {
            LocalDate startTime = LocalDate.parse(query.getStartCreateTime());
            LocalDate endTime = LocalDate.parse(query.getEndCreateTime());
            dateList = DateUtils.getDatesBetween(startTime, endTime);
        }

        List<LocalDate> finalDateList = dateList;
        List<String> cities = query.getCity();

        // 异步任务获取历史辐射数据
        CompletableFuture<List<DateRadiationVO>> radiationFuture = CompletableFuture.supplyAsync(() -> {
            List<DateRadiationVO> radiationList = new ArrayList<>();
            if (CollUtil.isNotEmpty(cities)) {
                List<CompletableFuture<List<DateRadiationVO>>> futures = cities.stream()
                        .map(city -> CompletableFuture.supplyAsync(() -> forecastService.getHistoryRadiationList(finalDateList, city)))
                        .collect(Collectors.toList());

                CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

                try {
                    allOf.get(); // Wait for all futures to complete
                    radiationList = futures.stream()
                            .map(CompletableFuture::join)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                } catch (InterruptedException | ExecutionException e) {
                    // Logging or handling the exception appropriately
                    log.error("Error occurred while fetching radiation data: {}", e.getMessage());
                    throw new BusinessException("Error occurred while fetching radiation data");
                }
            }
            return radiationList;
        });

        // 异步任务查询数据库
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        CompletableFuture<IPage<PowerPlantInfoVO>> dbQueryFuture = CompletableFuture.supplyAsync(
                () -> plantMapper.getPowerPlantInfoByLocation(query, userInfo, new Page<>(query.getCurrentPage(), query.getPageSize()))
        );

        // 获取结果
        List<DateRadiationVO> radiationList = radiationFuture.join();
        IPage<PowerPlantInfoVO> result = dbQueryFuture.join();

        // 处理结果
        List<PowerPlantInfoVO> records = result.getRecords();
        for (PowerPlantInfoVO item : records) {
            String plantCapacity = BusinessCalculateUtil.getRealPlantCapacity(item.getPlantCapacity());
            item.setPlantCapacity(plantCapacity);

            String electricity = BusinessCalculateUtil.getRealElectricity(item.getElectricity());
            item.setElectricity(electricity);

            String electricityEfficiency = BusinessCalculateUtil.getElectricityEfficiency(electricity, plantCapacity);
            item.setElectricityEfficiency(electricityEfficiency);

            for (DateRadiationVO radiationVO : radiationList) {
                LocalDate collectDate = item.getCollect().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                if (item.getCity().equals(radiationVO.getCity()) && collectDate.isEqual(LocalDate.parse(radiationVO.getDate()))) {
                    item.setRadiation(new BigDecimal(Optional.ofNullable(radiationVO.getRadiation()).orElse("0.0")).setScale(2, RoundingMode.HALF_UP).toString());
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 导出发电效率报表Excel
     *
     * @param query 查询参数
     * @param response 响应对象
     */
    @Override
    public void getPowerPlantInfoByLocationExcel(PowerPlantInfoQueryDTO query, HttpServletResponse response) {
        query.setPageSize(-1);
        query.setCurrentPage(-1);
        IPage<PowerPlantInfoVO> page = this.getPowerPlantInfoByLocation(query);
        List<PowerPlantInfoVO> records = page.getRecords();
        CreateExcelUtils.exportToExcel(records, PowerPlantInfoVO.class, "PowerPlantInfo", response);
    }

    /**
     * 获取电站发电量排行榜（分页）
     *
     * @param query 查询参数
     * @return 电站发电量排行榜分页数据
     */
    @Override
    public IPage<WorkEfficiencyVO> getPlantElectricityRank(PowerPlantInfoQueryDTO query) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        IPage<WorkEfficiencyVO> page = plantMapper.getPlantElectricityRank(query, userInfo, new Page<>(query.getCurrentPage(), query.getPageSize()));
        return page;
    }

    /**
     * 获取气象站内的电站名称列表
     *
     * @param deviceAddress 设备地址
     * @return 电站名称列表
     */
    @Override
    public List<String> getPlantNameListInWeatherStation(String deviceAddress) {
        return plantMapper.getPlantNameListInWeatherStation(deviceAddress);
    }

    /**
     * 通过pid获取城市
     *
     * @param projectId 项目id
     * @return 城市列表
     */
    @Override
    public List<String> getCitiesByPid(String projectId) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        return plantMapper.getCitiesByPid(userInfo, projectId);
    }

    /**
     * 注册新电站
     *
     * 创建新的电站记录，生成电站唯一标识，确保电站名称唯一性
     *
     * @param plant 电站实体对象
     * @return 创建完成的电站实体（包含生成的电站ID）
     * @throws BusinessException 当电站名称已存在时抛出异常
     */ 
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Plant registerPlant(Plant plant) {
        List<Plant> dbPlant = this.lambdaQuery()
                .eq(Plant::getPlantName, plant.getPlantName())
                .eq(Plant::getIsDeleted, 0)
                .list();
        if (CollUtil.isNotEmpty(dbPlant)) {
            throw new BusinessException("电站名已存在");
        }
        String plantUid = plantMapper.generateUid("plant");
        plant.setPlantUid(plantUid);
        this.save(plant);
        return plant;
    }
}
