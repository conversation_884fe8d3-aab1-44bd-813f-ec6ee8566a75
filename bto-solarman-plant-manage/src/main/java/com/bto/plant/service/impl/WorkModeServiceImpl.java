package com.bto.plant.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.PassiveModeDTO;
import com.bto.commons.pojo.dto.PassivePowerDTO;
import com.bto.commons.pojo.dto.SOCRetainDTO;
import com.bto.commons.pojo.dto.WorkModeConfigDTO;
import com.bto.commons.pojo.entity.BtoSetParameter;
import com.bto.plant.dao.BtoBatteryDao;
import com.bto.plant.service.WorkModeService;
import com.bto.redis.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 工作模式服务实现类
 * 
 * 工作模式配置相关业务服务的具体实现类，提供电池工作模式、充放电计划、SOC保留值、被动充放电等功能的配置和管理
 *
 * <AUTHOR> by zhb on 2025/4/16.
 */
@Service
public class WorkModeServiceImpl implements WorkModeService {

    @Autowired
    private BtoBatteryDao btoBatteryDao;

    @Autowired
    private RedisUtil redisUtil;
    private static final String SAJ_ACCESS_TOKEN_REDIS_KEY = "saj_access_token";
    private static final String BASE_URL = "https://developer.saj-electric.cn/prod-api/open/api";
    private static final String APP_ID = "VH_tNlaoNP8";
    private static final String APP_SECRET = "X3nKTolbGUM9x4KWL0w3vwBLoNP8hP3r36r46kdWVhxHtNla9gkYr02KGpsiOOmx";
    // 获取获取开发者access_token
    private static final String ACCESS_TOKEN_URL = BASE_URL + "/access_token";
    // 查询工作模式及充放电计划
    private static final String WORK_MODE_URL = BASE_URL + "/device/get/workModeTimeGet";
    // 设置工作模式及充放电计划
    private static final String SET_WORK_MODE_URL = BASE_URL + "/device/set/workModeTimeSet";
    // 查询后备电池soc保留值
    private static final String BACKUP_BATTERY_SOC_URL = BASE_URL + "/device/get/backModeSOC";
    // 设置后备电池soc保留值
    private static final String SET_BACKUP_BATTERY_SOC_URL = BASE_URL + "/device/set/backModeSOC";
    // 设置被动充放电使能
    private static final String SET_PASSIVE_CHARGE_DISCHARGE_ENABLE_URL = BASE_URL + "/device/set/passiveMode";
    // 设置被动充放电功率
    private static final String SET_PASSIVE_CHARGE_DISCHARGE_POWER_URL = BASE_URL + "/device/set/passivePower";
    // 查询被动充放电使能
    private static final String PASSIVE_CHARGE_DISCHARGE_ENABLE_URL = BASE_URL + "/device/get/passiveMode";
    // 查询被动充放电功率
    private static final String PASSIVE_CHARGE_DISCHARGE_POWER_URL = BASE_URL + "/device/get/passivePower";

    /**
     * 获取工作模式参数
     *
     * 根据设备序列号获取设备的工作模式参数配置
     *
     * @param deviceSn 设备序列号
     * @return 工作模式参数配置
     */
    @Override
    public BtoSetParameter getWorkModeParam(String deviceSn) {
        BtoSetParameter workModeParam = btoBatteryDao.getWorkModeParam(deviceSn);
        // 封装通用处理逻辑：解析、格式化、排序、返回
        Function<String, List<BtoSetParameter.ElectricityPlan>> processList = jsonStr ->
                Optional.ofNullable(JSONObject.parseArray(jsonStr, BtoSetParameter.ElectricityPlan.class))
                        .map(list -> {
                            list.forEach(p -> {  // 格式化时间
                                p.setStartTime(formatTime(p.getStartTime()));
                                p.setEndTime(formatTime(p.getEndTime()));
                            });
                            list.sort(Comparator.comparingInt(BtoSetParameter.ElectricityPlan::getNumber)); // 排序
                            return list;
                        })
                        .orElse(Collections.emptyList());
        // 设置处理后的列表
        workModeParam.setChargeList(processList.apply(workModeParam.getChargeListJsonStr()));
        workModeParam.setDischargeList(processList.apply(workModeParam.getDischargeListJsonStr()));
        return workModeParam;
    }

    /**
     * 格式化时间字符串（添加冒号）
     * 
     * 将原始时间字符串格式化为标准时间格式，如将"0630"格式化为"06:30"
     *
     * @param time 原始时间字符串（如 0630）
     * @return 格式化后的时间（如 06:30）
     */
    private static String formatTime(String time) {
        if (time == null) return null;
        if (time.contains(":")) {
            return time;
        }
        LocalTime t = LocalTime.parse(time, DateTimeFormatter.ofPattern("HHmm"));
        return DateTimeFormatter.ofPattern("HH:mm").format(t);
    }

    /**
     * 工作模式时间设置
     *
     * 设置设备的工作模式时间参数
     *
     * @param workModeConfigDTO 工作模式配置DTO
     * @return JSON响应对象
     */
    @Override
    public JSONObject workModeTimeSet(WorkModeConfigDTO workModeConfigDTO) {
        Integer mode = workModeConfigDTO.getAccUserMode();
        switch (mode) {
            case 1:
            case 3:
                break;
            case 2:
                // 分时模式处理充放电计划
                workModeConfigDTO.setChargeListJsonStr(JSONObject.toJSONString(workModeConfigDTO.getChargeList()));
                workModeConfigDTO.setDischargeListJsonStr(JSONObject.toJSONString(workModeConfigDTO.getDischargeList()));
                break;
            default:
                throw new BusinessException("暂不支持该工作模式");
        }
        return executeRequest(SET_WORK_MODE_URL, workModeConfigDTO, btoBatteryDao::updateWorkMode);
    }

    /**
     * 设置SOC保留值
     *
     * 设置设备的SOC（State of Charge）保留值
     *
     * @param socRetainDTO SOC保留DTO
     * @return JSON响应对象
     */
    @Override
    public JSONObject backModeSOCSet(SOCRetainDTO socRetainDTO) {
        return executeRequest(SET_BACKUP_BATTERY_SOC_URL, socRetainDTO, btoBatteryDao::backModeSOCSet);
    }

    /**
     * 设置被动模式
     *
     * 设置设备的被动工作模式参数
     *
     * @param passiveModeDTO 被动模式DTO
     * @return JSON响应对象
     */
    @Override
    public JSONObject passiveModeSet(PassiveModeDTO passiveModeDTO) {
        return executeRequest(SET_PASSIVE_CHARGE_DISCHARGE_ENABLE_URL, passiveModeDTO, btoBatteryDao::passiveModeSet);
    }

    /**
     * 设置被动功率
     *
     * 设置设备的被动功率参数
     *
     * @param passivePowerDTO 被动功率DTO
     * @return JSON响应对象
     */
    @Override
    public JSONObject passivePowerSet(PassivePowerDTO passivePowerDTO) {
        BtoSetParameter workModeParam = getWorkModeParam(passivePowerDTO.getDeviceSn());
        if (Objects.isNull(passivePowerDTO.getPassiveGridChargePower())) {
            passivePowerDTO.setPassiveGridChargePower(workModeParam.getPassiveGridChargePower());
        }
        if (Objects.isNull(passivePowerDTO.getPassiveGridDisChargePower())) {
            passivePowerDTO.setPassiveGridDisChargePower(workModeParam.getPassiveGridDischargePower());
        }
        return executeRequest(SET_PASSIVE_CHARGE_DISCHARGE_POWER_URL, passivePowerDTO, btoBatteryDao::passivePowerSet);
    }

    // 发送POST请求
    private JSONObject postRequest(String url, Object param) {
        String response = HttpRequest.post(url)
                .headerMap(getHeaders(), false)
                .body(JSONObject.toJSONString(param))
                .execute()
                .body();
        return JSONObject.parseObject(response);
    }

    // 通用请求执行方法
    private <T> JSONObject executeRequest(String url, T param, Consumer<T> updateAction) {
        JSONObject json = postRequest(url, param);
        processResponse(json, () -> updateAction.accept(param));
        return json.getJSONObject("data");
    }

    // 响应处理
    private void processResponse(JSONObject json, Runnable onSuccess) {
        if (json.getIntValue("code") != HttpStatus.HTTP_OK) {
            throw new BusinessException(json.getString("msg"));
        }
        onSuccess.run();
    }

    // 获取请求头
    public HashMap<String, String> getHeaders() {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("content-language", "zh_CN");
        hashMap.put("Content-Type", "application/json");
        hashMap.put("accessToken", getAccessToken());
        return hashMap;
    }

    // 获取access_token
    public String getAccessToken() {
        if (redisUtil.hasKey(SAJ_ACCESS_TOKEN_REDIS_KEY)) {
            return (String) redisUtil.get(SAJ_ACCESS_TOKEN_REDIS_KEY);
        }
        HttpResponse response = HttpRequest.get(ACCESS_TOKEN_URL)
                .header("content-language", "zh_CN")
                .form("appId", APP_ID)
                .form("appSecret", APP_SECRET)
                .execute();
        if (response.getStatus() == HttpStatus.HTTP_OK) {
            JSONObject resultJson = JSONObject.parseObject(response.body());
            if (resultJson.getIntValue("code") == HttpStatus.HTTP_OK) {
                JSONObject data = resultJson.getJSONObject("data");
                String accessToken = data.getString("access_token");
                redisUtil.set(SAJ_ACCESS_TOKEN_REDIS_KEY, accessToken, 60 * 60 * 6L);
                return accessToken;
            }
        } else {
            throw new BusinessException("HTTP请求失败，状态码: " + response.getStatus());
        }
        return null;
    }
}
