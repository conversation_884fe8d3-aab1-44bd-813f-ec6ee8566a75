package com.bto.plant.service.impl;

import com.bto.commons.pojo.entity.MeteorologyEntity;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.MeteorologyMapper;
import com.bto.plant.service.MeteorologyService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 采集气象信息服务实现类
 * 
 * 气象数据采集相关业务服务的具体实现类，提供气象信息的存储和管理功能。
 *
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-05
 */
@Service
@AllArgsConstructor
public class MeteorologyServiceImpl extends BaseServiceImpl<MeteorologyMapper, MeteorologyEntity> implements MeteorologyService {

}