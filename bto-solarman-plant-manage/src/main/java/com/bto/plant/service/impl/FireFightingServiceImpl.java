package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.entity.HourTownEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.FireFightingDao;
import com.bto.plant.service.FireFightingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消防联动服务实现类
 * 
 * 消防联动设备相关业务服务的具体实现类，提供消防设备的分页查询和管理功能。
 * 支持与设备管理服务的集成，确保消防设备数据的完整性和准确性。
 * 
 * <AUTHOR>
 * @since 2024/10/7 15:52
 */
@Service
@RequiredArgsConstructor
public class FireFightingServiceImpl extends ServiceImpl<FireFightingDao, FireFightingEntity> implements FireFightingService {
    public final GlobalParamUtil globalParamUtil;
    public final DeviceServiceClient deviceServiceClient;

    /**
     * 分页查询消防设备信息
     * 
     * 根据查询条件获取消防联动设备的分页数据，通过设备服务获取设备列表，
     * 确保只查询已配置的消防设备。
     * 
     * 数据处理流程：
     * - 通过设备管理服务获取指定电站的消防设备ID列表
     * - 验证设备列表的有效性，确保设备存在
     * - 执行分页查询获取消防设备详细信息
     * - 返回结构化的分页结果数据
     * 
     * @param query 查询条件（包含电站ID、分页参数等）
     * @return 消防设备信息分页结果
     * @throws BusinessException 当电站不存在消防设备时抛出异常
     */ 
    @Override
    public PageResult<FireFightingEntity> page(FireFightingQuery query) {
        Result<List<String>> result = deviceServiceClient.getListByType(query.getPlantUid(), DeviceType.FIRE_FIGHTING.getCode());
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())){
            List<String> ids = result.getData(new TypeReference<List<String>>() {
            });
            if (CollUtil.isEmpty(ids)){
                throw new BusinessException("电站不存在消防联动设备");
            }
            query.setId(ids);
        }
        Page<FireFightingEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 
     * 根据查询条件构建MyBatis-Plus查询条件包装器，支持消防设备的多维度查询。
     * 支持按更新时间降序排序，确保最新数据优先展示，提供灵活的筛选功能。
     * 
     * @param query 查询条件，包含消防设备相关的筛选参数
     * @return 查询条件包装器，包含完整的查询条件和排序规则
     */ 
    private LambdaQueryWrapper<FireFightingEntity> getWrapper(FireFightingQuery query) {
        LambdaQueryWrapper<FireFightingEntity> wrapper = Wrappers.lambdaQuery();
        wrapper
                .orderByDesc(FireFightingEntity::getUpdateTime)
        ;

        return wrapper;
    }


}