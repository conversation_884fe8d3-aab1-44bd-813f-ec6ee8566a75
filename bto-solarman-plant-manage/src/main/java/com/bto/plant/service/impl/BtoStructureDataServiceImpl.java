package com.bto.plant.service.impl;

import com.bto.commons.pojo.entity.BtoStructureDataEntity;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.BtoStructureDataDao;
import lombok.AllArgsConstructor;
import com.bto.plant.service.BtoStructureDataService;
import org.springframework.stereotype.Service;

/**
 * 结构件监控数据服务实现类
 * 
 * 结构件监控数据相关业务服务的具体实现类，提供结构件监控数据的增删改查等基础服务。
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
@AllArgsConstructor
public class BtoStructureDataServiceImpl extends BaseServiceImpl<BtoStructureDataDao, BtoStructureDataEntity> implements BtoStructureDataService {

}