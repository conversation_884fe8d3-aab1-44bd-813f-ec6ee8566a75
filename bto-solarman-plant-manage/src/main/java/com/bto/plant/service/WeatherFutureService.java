package com.bto.plant.service;


import com.bto.commons.pojo.entity.WeatherFutureEntity;
import com.bto.commons.pojo.vo.WeatherFutureVO;
import com.bto.commons.service.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * 未来天气服务接口
 * 
 * 未来天气预测相关业务服务的接口定义，提供未来15天的天气数据查询功能。
 *
 * <AUTHOR> 
 * @since 1.0.0 2024-01-05
 */
public interface WeatherFutureService extends BaseService<WeatherFutureEntity> {

    /**
     * 获取未来天气列表
     * 
     * 根据未来日期列表和城市列表查询未来天气信息，支持批量查询。
     *
     * @param futureDateList 未来日期列表
     * @param city 城市列表
     * @return 未来天气视图对象列表
     */
    List<WeatherFutureVO> getFutureWeatherList(List<LocalDate> futureDateList, List<String> city);
}