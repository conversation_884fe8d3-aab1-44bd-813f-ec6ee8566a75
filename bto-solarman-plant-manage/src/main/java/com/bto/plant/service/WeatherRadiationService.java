package com.bto.plant.service;


import com.bto.commons.pojo.entity.WeatherRadiationEntity;
import com.bto.commons.pojo.vo.DateRadiationVO;
import com.bto.commons.service.BaseService;

import java.time.LocalDate;

/**
 * 天气与辐射量关系服务接口
 * 
 * 天气与辐射量关系相关业务服务的接口定义，提供天气条件下的辐射量查询和历史数据获取功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2024-01-06
 */
public interface WeatherRadiationService extends BaseService<WeatherRadiationEntity> {

    /**
     * 根据天气获取辐射量
     * 
     * 根据天气状况查询对应的辐射量数值，用于发电量计算和天气分析。
     *
     * @param weatherOne 天气状况
     * @return 辐射量数值
     */
    Double getRadiationByWeather(String weatherOne);

    /**
     * 获取历史辐射量数据
     * 
     * 根据日期和城市查询历史天气条件下的辐射量数据。
     *
     * @param localDate 查询日期
     * @param city 城市名称
     * @return 日期辐射量视图对象
     */
    DateRadiationVO getHistoryRadiationByWeather(LocalDate localDate, String city);
}