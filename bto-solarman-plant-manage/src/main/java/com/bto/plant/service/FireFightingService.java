package com.bto.plant.service;

import com.bto.commons.pojo.dto.FireFightingQuery;
import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.service.BaseService;

/**
 * 消防服务接口
 * 
 * 提供消防相关的业务服务，包括消防设备管理、数据分页查询等功能
 *
 * <AUTHOR>
 * @since 2024/10/7 15:52
 */
public interface FireFightingService extends BaseService<FireFightingEntity> {

    /**
     * 分页查询消防设备数据
     * 
     * 根据查询条件分页获取消防设备信息列表
     *
     * @param query 消防设备查询条件对象，包含分页和过滤参数
     * @return PageResult<FireFightingEntity> 分页结果对象，包含消防设备实体信息
     */
    PageResult<FireFightingEntity> page(FireFightingQuery query);

}