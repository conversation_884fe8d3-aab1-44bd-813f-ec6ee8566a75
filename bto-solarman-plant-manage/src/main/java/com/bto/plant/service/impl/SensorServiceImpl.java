package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.DeviceType;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.SensorQuery;
import com.bto.commons.pojo.entity.SensorEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.DateUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.dao.SensorDao;
import com.bto.plant.service.SensorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 传感器服务实现类
 * 
 * 传感器设备相关业务服务的具体实现类，提供传感器设备的分页查询、详细数据查询等功能
 * 
 *
 * <AUTHOR>
 * @since 2024/10/7 16:26
 */
@Service
@RequiredArgsConstructor
public class SensorServiceImpl extends ServiceImpl<SensorDao, SensorEntity> implements SensorService {
    public final GlobalParamUtil globalParamUtil;
    public final DeviceServiceClient deviceServiceClient;


    /**
     * 获取传感器详细数据
     * 
     * 根据传感器ID和日期获取该传感器在指定日期的详细数据列表，按创建时间升序排列
     *
     * @param sensorId 传感器ID
     * @param date 查询日期
     * @return 传感器实体列表
     */
    @Override
    public List<SensorEntity> detail(String sensorId, Date date) {
        List<SensorEntity> list = this.lambdaQuery()
                .gt(SensorEntity::getCreateTime, DateUtils.getDayStart(date))
                .eq(SensorEntity::getSensorId, sensorId)
                .orderByAsc(SensorEntity::getCreateTime)
                .list();
        return list;
    }

    /**
     * 分页查询传感器信息
     * 
     * 根据查询条件获取传感器设备的分页数据，通过设备服务获取传感器设备列表，确保只查询已配置的传感器设备
     *
     * @param query 查询条件（包含电站ID、分页参数等）
     * @return 传感器信息分页结果
     * @throws BusinessException 当电站不存在传感设备时抛出异常
     */
    @Override
    public PageResult<SensorEntity> page(SensorQuery query) {
        Result<List<String>> result = deviceServiceClient.getListByType(query.getPlantUid(), DeviceType.ENVIRONMENT_SENSOR.getCode());
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())){
            List<String> ids = result.getData(new TypeReference<List<String>>() {
            });
            if (CollUtil.isEmpty(ids)){
                throw new BusinessException("电站不存在传感设备");
            }
            query.setSensorId(ids);
        }

        Page<SensorEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        page = baseMapper.page(page, query, null);

        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 构建查询条件包装器
     * 
     * 根据查询条件构建MyBatis-Plus查询条件包装器
     *
     * @param query 查询条件
     * @return 查询条件包装器
     */
    private static LambdaQueryWrapper<SensorEntity> getWrapper(SensorQuery query) {
        LambdaQueryWrapper<SensorEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .like(query.getSensorId() != null, SensorEntity::getSensorId, query.getSensorId());
        return wrapper;
    }
}
