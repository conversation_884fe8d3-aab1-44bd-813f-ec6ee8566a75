package com.bto.plant.service;


import com.bto.commons.pojo.entity.RadiantQuantityEntity;
import com.bto.commons.service.BaseService;
import com.bto.commons.pojo.dto.RadiantQuantityQuery;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 辐射量服务接口
 * 
 * 提供太阳辐射量相关的业务服务，包括辐射量查询、预测计算、历史数据分析等功能
 *
 * <AUTHOR>
 * @since 1.0.0 2023-12-29
 */
public interface RadiantQuantityService extends BaseService<RadiantQuantityEntity> {

    /**
     * 获取最新辐射量
     * 
     * 根据气象站唯一标识符获取当前时间最新的太阳辐射量
     *
     * @param weatherStationUid 气象站唯一标识符
     * @return String 辐射量数值字符串
     */
    String getRadiate(String weatherStationUid);

    /**
     * 根据条件获取辐射量
     * 
     * 根据查询条件获取指定条件下的太阳辐射量
     *
     * @param query 辐射量查询条件对象
     * @return BigDecimal 辐射量数值
     */
    BigDecimal getRadiateByCondition(RadiantQuantityQuery query);

    /**
     * 获取30天辐射量数据
     * 
     * 根据气象站列表和缓存键获取过去30天的辐射量数据
     *
     * @param stationUidList 气象站唯一标识符列表
     * @param redisKey Redis缓存键
     * @return List<Map<String, String>> 辐射量数据映射列表
     */
    List<Map<String, String>> getRadiateByThirtyDays(List<String> stationUidList,String redisKey);

    /**
     * 根据辐射量预测电量
     * 
     * 根据太阳辐射量和项目ID预测发电量
     *
     * @param radiation 太阳辐射量
     * @param projectId 项目唯一标识符
     * @return String 预测电量结果
     */
    String getPredictionElectricityByRadiation(String radiation,String projectId);

}