package com.bto.plant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.commons.pojo.dto.CounterQuery;
import com.bto.commons.pojo.entity.CounterEntity;
import com.bto.commons.pojo.vo.CounterVO;
import com.bto.commons.pojo.vo.PageResult;

/**
 * 计数器服务接口
 * 
 * 提供计数相关的业务服务，包括计数器数据的分页查询和管理功能
 *
 * <AUTHOR>
 * @since 2024/10/7 15:52
 */
public interface CounterService extends IService<CounterEntity> {

    /**
     * 分页查询计数器数据
     * 
     * 根据查询条件分页获取计数器信息列表
     *
     * @param query 计数器查询条件对象，包含分页和过滤参数
     * @return PageResult<CounterVO> 分页结果对象，包含计数器视图信息
     */
    PageResult<CounterVO> page(CounterQuery query);
}
