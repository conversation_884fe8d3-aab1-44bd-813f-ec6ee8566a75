package com.bto.plant.service;

import com.bto.commons.pojo.dto.FunctionalInstrumentQuery;
import com.bto.commons.pojo.entity.FunctionalInstrumentEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.service.BaseService;

/**
 * 多功能仪表数据服务接口
 * 
 * 提供多功能仪表数据相关的业务服务，包括仪表数据管理、分页查询等功能
 *
 * <AUTHOR> 
 * @since 2024-10-09
 */
public interface FunctionalInstrumentService extends BaseService<FunctionalInstrumentEntity> {

    /**
     * 分页查询多功能仪表数据
     * 
     * 根据查询条件分页获取多功能仪表信息列表
     *
     * @param query 多功能仪表查询条件对象，包含分页和过滤参数
     * @return PageResult<FunctionalInstrumentEntity> 分页结果对象，包含仪表实体信息
     */
    PageResult<FunctionalInstrumentEntity> page(FunctionalInstrumentQuery query);

}