package com.bto.plant.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.OrientationEnum;
import com.bto.commons.constant.ProjectTypeEnum;
import com.bto.commons.enums.WindEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.*;
import com.bto.commons.pojo.dto.PlantVO;
import com.bto.commons.pojo.entity.MeteorologyEntity;
import com.bto.commons.pojo.entity.PlantInfoEntity;
import com.bto.commons.pojo.entity.WeatherEntity;
import com.bto.commons.pojo.vo.*;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.BusinessCalculateUtil;
import com.bto.commons.utils.DateUtils;
import com.bto.commons.utils.RangeUtils;
import com.bto.oauth.global.GlobalParamUtil;
import com.bto.plant.convert.ForecastBaseInfoConvert;
import com.bto.plant.convert.PlantAddressConvert;
import com.bto.plant.convert.PlantInfoConvert;
import com.bto.plant.dao.*;
import com.bto.plant.service.*;
import com.bto.redis.utils.RedisUtil;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bto.commons.constant.RedisKey.*;
import static com.bto.commons.response.ResultEnum.DATETIME_FORMAT_FAILED;

/**
 * 预测服务实现类
 * 
 * 电站发电预测相关业务服务的具体实现类，提供辐射量计算、发电量预测、历史数据分析、天气数据获取等功能。
 * 
 * <AUTHOR> by zhb on 2023/12/21.
 */

@Service
@AllArgsConstructor

public class ForecastServiceImpl implements ForecastService {

    private final MeteorologyMapper meteorologyMapper;
    private final WeatherRadiationService weatherRadiationService;
    private final WeatherMapper weatherMapper;
    private final ForecastMapper forecastMapper;
    private final BtoPlantInfoService plantInfoService;
    private final PlantService plantService;
    private final WeatherFutureService weatherFutureService;
    private final PlantMapper plantMapper;
    private final GlobalParamUtil globalParamUtil;
    private final RadiantQuantityDao radiantQuantityDao;
    private final RedisUtil redisUtil;
    private final DeviceServiceClient deviceServiceClient;
    private static final BigDecimal ELECTRICITY_BENEFIT_MULTIPLIER = new BigDecimal(0.45).setScale(2, RoundingMode.HALF_UP);

    private final WeatherStationService weatherStationService;
    private final CarouselService carouselService;
    private final RadiantQuantityService radiantQuantityService;

    /**
     * 根据电站ID获取气象数据列表
     * 
     * 查询指定电站在指定时间范围内的气象数据，通过电站ID获取关联的气象站ID，
     * 然后从气象数据表中获取详细的气象信息。
     * 
     * @param plantId 电站唯一标识
     * @param begin 查询开始时间（格式：yyyy-MM-dd）
     * @param end 查询结束时间（格式：yyyy-MM-dd）
     * @return 气象数据实体列表，包含温度、湿度、风速等气象信息
     * @throws BusinessException 当电站所在地无气象站时抛出异常
     */
    @Override
    @DS("slave")
    public List<MeteorologyEntity> getMeteorologyListByPlantId(String plantId, String begin, String end) {
        String weatherStationUid = forecastMapper.getWeatherStationUid(plantId);
        if (StrUtil.isEmpty(weatherStationUid)) {
            throw new BusinessException("电站ID：" + plantId + "所在地无气象站，无法提供发电预测数据");
        }
        return meteorologyMapper.selectByCriteria(begin, end, weatherStationUid);
    }

    /**
     * 获取实际预测电量
     *
     * 根据查询条件获取实际发电量与预测电量的对比数据
     *
     * @param query 实际预测电量查询数据传输对象
     * @return List<RealityForecastElectricityVO> 实际预测电量视图对象列表
     */
    @Override
    public List<RealityForecastElectricityVO> getRealityForecastElectricity(RealityForecastElectricityDTO query) {
        return forecastMapper.getRealityForecastElectricity(query);
    }

    /**
     * 根据地址获取辐射量
     *
     * 根据电站地址信息和指定日期计算太阳辐射量
     *
     * @param plantAddressDTO 电站地址数据传输对象
     * @param dateTime 指定日期时间
     * @return Double 太阳辐射量数值
     */
    @Override
    @DS("slave")
    public Double getRadiationByAddress(PlantAddressDTO plantAddressDTO, Date dateTime) {
        String city = plantAddressDTO.getCity();
        String begin = DateUtils.getDayStart(dateTime);
        String end = DateUtils.getDayEnd(dateTime);
        if (StrUtil.isEmpty(city)) {
            throw new BusinessException("电站地址信息缺失，无法获取天气信息");
        }
        String time = DateUtils.getCurrentMonth(dateTime);
        List<WeatherEntity> weatherEntities = weatherMapper.selectList("bto_weather_" + time, begin, end, plantAddressDTO.getCity());
        List<WeatherRadiationVO> weatherRadiations = weatherMapper.selectWeatherRadiation("bto_weather_" + time, begin, end, city);
        return acquiredRadiation(weatherEntities, weatherRadiations);
    }

    /**
     * 根据城市获取辐射量
     *
     * 根据城市名称和指定日期获取该城市的太阳辐射量
     *
     * @param city 城市名称
     * @param dateTime 指定日期时间
     * @return Double 太阳辐射量数值
     */
    @Override
    @DS("slave")
    public Double getRadiationByCity(String city, Date dateTime) {
        String dateStr = DateUtil.format(dateTime, "yyyy-MM-dd");
        String time = DateUtils.getCurrentMonth(dateTime);
        return weatherMapper.getRadiationByCity("bto_weather_" + time, dateStr + "%", city);
    }

    /**
     * 获取未来预测数据
     *
     * 根据电站ID、辐射量和指定日期计算未来发电预测数据
     *
     * @param plantId 电站唯一标识符
     * @param radiation 太阳辐射量
     * @param dateTime 指定日期时间
     * @return BatteryDivinerVO 电池预测数据视图对象
     */
    @Override
    public BatteryDivinerVO getFutureForecastData(String plantId, Double radiation, Date dateTime) {
        String futureForecastData = forecastMapper.getFutureForecastData(plantId, radiation);
        if (Objects.isNull(futureForecastData)) {
            throw new BusinessException("电站ID：" + plantId + "所在地无气象站，无法提供发电预测数据");
        }
        BatteryDivinerVO batteryDiviner = new BatteryDivinerVO();
        batteryDiviner.setElectricity(futureForecastData);
        batteryDiviner.setCollectDate(DateUtil.format(dateTime, "yyyy-MM-dd"));
        return batteryDiviner;
    }

    /**
     * 根据城市获取历史电量
     *
     * 根据城市名称获取该城市历史发电电量数据
     *
     * @param city 城市名称
     * @return List<BatteryDivinerVO> 电池预测数据视图对象列表
     */
    @Override
    public List<BatteryDivinerVO> getHistoryElectricityByCity(String city) {
        // 历史七天实际发电量
        return plantService.getElectricityByCity(city);
    }

    /**
     * 根据城市获取日期辐射量
     *
     * 使用主数据源，根据城市名称获取每日辐射量数据
     *
     * @param city 城市名称
     * @return HashMap<String, List<DateRadiationVO>> 日期辐射量映射
     */
    @Override
    public HashMap<String, List<DateRadiationVO>> getDateRadiationByCity(String city) {
        HashMap<String, List<DateRadiationVO>> hashMap = new HashMap<>();
        List<DateRadiationVO> futureRadiationList = new ArrayList<>();
        List<DateRadiationVO> historyFutureRadiation = new ArrayList<>();
        List<DateRadiationVO> historyRadiationList =new ArrayList<>();
        List<List<?>> predictElectricityByCity = forecastMapper.getPredictElectricityByCity(city);
        List<PredictRegionalElectricityVO> predictRegionalElectricityVos = (List<PredictRegionalElectricityVO>) predictElectricityByCity.get(1);
        List<RegionalElectricityVO> regionalElectricityVos = (List<RegionalElectricityVO>) predictElectricityByCity.get(0);
        Collections.reverse(regionalElectricityVos);
        for (PredictRegionalElectricityVO entity : predictRegionalElectricityVos) {
            DateRadiationVO dateRadiationVO = new DateRadiationVO(entity.getCollectDate(), entity.getFutureRadiant(), city);
            futureRadiationList.add(dateRadiationVO);
        }
        for (RegionalElectricityVO regionalElectricityVo : regionalElectricityVos) {
            DateRadiationVO dateRadiationVO = new DateRadiationVO(regionalElectricityVo.getCollectDate(), regionalElectricityVo.getPredictQuantity(), city);
            historyFutureRadiation.add(dateRadiationVO);
            DateRadiationVO dateRadiation = new DateRadiationVO(regionalElectricityVo.getCollectDate(), regionalElectricityVo.getRadiantQuantity(), city);
            historyRadiationList.add(dateRadiation);
        }
        historyFutureRadiation.remove(historyRadiationList.size() - 1);
        DateRadiationVO today = futureRadiationList.get(0);
        historyFutureRadiation.add(today);
        hashMap.put("futureRadiationList", futureRadiationList);
        hashMap.put("historyFutureRadiation", historyFutureRadiation);
        hashMap.put("historyRadiationList", historyRadiationList);
        return hashMap;
    }


    /**
     * 获取历史七天日期列表
     * 生成从今天往前推7天的日期列表，用于查询历史天气和发电量数据
     * 
     * @return List<LocalDate> 历史七天日期列表，按时间升序排列
     */
    public List<LocalDate> getHistorySevenDaysDateList() {
        LocalDate today = LocalDate.now();
        // 创建一个日期流，从今天开始，往前推7天
        Stream<LocalDate> dateStream = Stream.iterate(today, date -> date.minusDays(1));
        List<LocalDate> historyDateList = dateStream.limit(7).collect(Collectors.toList());
        Collections.reverse(historyDateList);
        return historyDateList;
    }

    /**
     * 获取未来天气列表
     * 生成从明天开始往后推size天的日期列表，用于查询未来天气数据
     *
     * @param city 城市列表
     * @param size 预测天数
     * @return List<WeatherFutureVO> 未来天气视图对象列表
     */
    public List<WeatherFutureVO> getFutureSevenDayWeatherList(List<String> city, int size) {

        LocalDate today = LocalDate.now();
        // 往后推size天
        Stream<LocalDate> futureDateStream = Stream.iterate(today.plusDays(1), date -> date.plusDays(1));
        List<LocalDate> futureDateList = futureDateStream.limit(size).collect(Collectors.toList());
        return weatherFutureService.getFutureWeatherList(futureDateList, city);
    }

    /**
     * 根据城市获取未来预测
     *
     * 根据城市名称和城市辐射量获取未来发电预测结果
     *
     * @param city 城市名称
     * @param cityRadiation 城市辐射量
     * @return String 预测结果字符串
     */
    @Override
    public String getFutureForecastByCity(String city, Double cityRadiation) {
        return forecastMapper.getFutureForecastByCity(city, cityRadiation);
    }

    /**
     * 根据城市获取天气
     *
     * 根据城市名称获取该城市的天气预报信息
     *
     * @param city 城市名称
     * @return HashMap<String, List<WeatherFutureVO>> 天气预测映射
     */
    @Override
    @DS("slave")
    public HashMap<String, List<WeatherFutureVO>> getWeatherByCity(String city) {
        HashMap<String, List<WeatherFutureVO>> hashMap = new HashMap<>();
        // 未来七天天气情况
        List<WeatherFutureVO> futureWeatherList = getFutureSevenDayWeatherList(Collections.singletonList(city), 7);
        hashMap.put("futureWeatherList", futureWeatherList);
        // 历史七天天气情况
        List<LocalDate> historySevenDaysDateList = getHistorySevenDaysDateList();
        // 判断第一天和最后一天是否为同月，不同月需要分表查询
        LocalDate firstDay = historySevenDaysDateList.get(0);
        LocalDate lastDay = historySevenDaysDateList.get(historySevenDaysDateList.size() - 1);
        String startOfDay = firstDay.atStartOfDay().toString();
        String lastOfDay = lastDay.atTime(23, 59, 59).toString();
        if (firstDay.getMonth().equals(lastDay.getMonth())) {
            // 同月份
            List<WeatherFutureVO> historySevenDaysWeather = weatherMapper.getHistorySevenDaysWeather("bto_weather_" + DateUtils.getCurrentMonth(lastDay), historySevenDaysDateList, city, startOfDay, lastOfDay);
            hashMap.put("historySevenDaysWeather", historySevenDaysWeather);
        } else {
            // 不同月份
            int crossMonthIndexes = 0;
            for (int i = 0; i < historySevenDaysDateList.size() - 1; i++) {
                LocalDate currentDate = historySevenDaysDateList.get(i);
                LocalDate nextDate = historySevenDaysDateList.get(i + 1);
                if (currentDate.getMonthValue() != nextDate.getMonthValue()) {
                    // 记录跨月份日期的索引
                    crossMonthIndexes = i;
                }
            }
            //     上个月数据
            LocalDate lastMonth = historySevenDaysDateList.get(crossMonthIndexes);
            String lastMonthOfDay = lastMonth.atTime(23, 59, 59).toString();
            List<LocalDate> lastMonthDate = historySevenDaysDateList.subList(0, crossMonthIndexes == 0 ? 1 : crossMonthIndexes);
            List<WeatherFutureVO> lastMonthWeather = weatherMapper.getHistorySevenDaysWeather("bto_weather_" + DateUtils.getCurrentMonth(lastMonth), lastMonthDate, city, startOfDay, lastMonthOfDay);
            //     下个月数据
            LocalDate monthOneDay = historySevenDaysDateList.get(crossMonthIndexes + 1);
            String monthOneDayStr = monthOneDay.atStartOfDay().toString();
            List<LocalDate> monthDate = historySevenDaysDateList.subList(crossMonthIndexes + 1, 6);
            List<WeatherFutureVO> monthDateWeather = weatherMapper.getHistorySevenDaysWeather("bto_weather_" + DateUtils.getCurrentMonth(monthOneDay), monthDate, city, monthOneDayStr, lastOfDay);
            //     汇总数据
            lastMonthWeather.addAll(monthDateWeather);
            hashMap.put("historySevenDaysWeather", lastMonthWeather);
        }
        return hashMap;
    }

    /**
     * 根据城市获取电量
     *
     * 根据城市名称获取该城市的电量数据
     *
     * @param city 城市名称
     * @return HashMap<String, List<BatteryDivinerVO>> 电量数据映射
     */
    @Override
    public HashMap<String, List<BatteryDivinerVO>> getElectricityByCity(String city) {
        HashMap<String, List<BatteryDivinerVO>> hashMap = new HashMap<>();
        // 获取近14天辐射量
        List<BatteryDivinerVO> historyFutureElectricity = new ArrayList<>();
        List<BatteryDivinerVO> historyRealElectricity = new ArrayList<>();
        List<BatteryDivinerVO> futureFutureElectricity = new ArrayList<>();

        List<List<?>> predictElectricityByCity = forecastMapper.getPredictElectricityByCity(city);
        List<PredictRegionalElectricityVO> predictRegionalElectricityVos = (List<PredictRegionalElectricityVO>) predictElectricityByCity.get(1);
        List<RegionalElectricityVO> regionalElectricityVos = (List<RegionalElectricityVO>) predictElectricityByCity.get(0);
        regionalElectricityVos.sort(Comparator.comparing(RegionalElectricityVO::getCollectDate));
        regionalElectricityVos.forEach(regionalElectricityVO -> {
            String collectDate = regionalElectricityVO.getCollectDate();
            BatteryDivinerVO historyFuture = new BatteryDivinerVO();
            historyFuture.setCollectDate(collectDate);
            historyFuture.setElectricity(regionalElectricityVO.getPredictElectricity());
            historyFutureElectricity.add(historyFuture);
            BatteryDivinerVO historyReal = new BatteryDivinerVO();
            historyReal.setCollectDate(collectDate);
            historyReal.setElectricity(regionalElectricityVO.getElectricity());
            historyRealElectricity.add(historyReal);
        });
        predictRegionalElectricityVos.forEach(predictRegionalElectricityVO -> {
            BatteryDivinerVO batteryDivinerVO = new BatteryDivinerVO();
            batteryDivinerVO.setCollectDate(predictRegionalElectricityVO.getCollectDate());
            batteryDivinerVO.setElectricity(predictRegionalElectricityVO.getFutureElectricity());
            futureFutureElectricity.add(batteryDivinerVO);
        });
        // historyFutureElectricity.remove(historyFutureElectricity.size() - 1);
        // BatteryDivinerVO batteryDivinerVO = futureFutureElectricity.get(0);
        // historyFutureElectricity.add(batteryDivinerVO);
        hashMap.put("historyFutureElectricity", historyFutureElectricity);
        hashMap.put("futureFutureElectricity", futureFutureElectricity);
        hashMap.put("historyRealElectricity", historyRealElectricity);
        return hashMap;
    }

    /**
     * 获取历史辐射量列表
     *
     * 根据日期列表和城市获取历史太阳辐射量数据
     *
     * @param dates 日期列表
     * @param city 城市名称
     * @return List<DateRadiationVO> 日期辐射量视图对象列表
     */
    @Override
    public List<DateRadiationVO> getHistoryRadiationList(List<LocalDate> dates, String city) {
        return meteorologyMapper.getHistoryRadiationList(city, dates);
    }

    /**
     * 获取预测电站列表
     *
     * 根据查询条件分页获取预测相关的电站列表
     *
     * @param query 预测电站查询数据传输对象
     * @return IPage<ForecastPlantVO> 分页结果对象
     */
    @Override
    public IPage<ForecastPlantVO> getForecastPlantList(ForecastPlantDTO query) {
        IPage<ForecastPlantVO> iPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        return forecastMapper.getForecastPlantList(userInfo, iPage, query);
    }

    /**
     * 获取预测电站列表
     *
     * @param query 查询参数
     * @return IPage<ForecastPlantVO> 分页结果对象
     */
    public IPage<ForecastPlantVO> getForecastPlantListByCity(ForecastPlantDTO query) {
        IPage<ForecastPlantVO> iPage = new Page<>(query.getCurrentPage(), query.getPageSize());
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        return forecastMapper.getForecastPlantListByCity(userInfo, iPage, query);
    }

    /**
     * 获取评价等级的电站列表
     *
     * @param query 查询参数
     * @return HashMap<String, Object> 评价等级的电站列表
     */
    @Override
    public HashMap<String, Object> getGradeEvaluationPlantList(ForecastPlantDTO query) {
        String date = query.getDate();
        if (StrUtil.isEmpty(date) || !DateUtils.checkDate(date)) {
            throw new BusinessException(DATETIME_FORMAT_FAILED);
        }

        Integer currentPage = query.getCurrentPage();
        Integer pageSize = query.getPageSize();
        query.setCurrentPage(-1);
        query.setPageSize(-1);
        List<ForecastPlantVO> records = new ArrayList<>();
        if (DateUtils.getTodayDate().equals(date)) {
            IPage<ForecastPlantVO> forecastPlantList = getForecastPlantList(query);
            records = forecastPlantList.getRecords();
        } else {
            IPage<ForecastPlantVO> forecastPlantListByCity = getForecastPlantListByCity(query);
            records = forecastPlantListByCity.getRecords();
        }
        int recordsSize = records.size();
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("total", recordsSize);
        List<String> predefinedIntervals = Arrays.asList(
                "0~10", "10~20", "20~30", "30~40", "40~50",
                "50~60", "60~70", "70~80", "80~90", "90~100"
        );
        records.forEach(forecastPlant -> {
            String forecastElectricity = forecastPlant.getForecastElectricity();
            if (StrUtil.isNotBlank(forecastElectricity)) {
                Double forecastElectricityValue = Double.parseDouble(forecastElectricity);
                Double todayElectricityValue = Double.parseDouble(forecastPlant.getTodayElectricity());
                double result = todayElectricityValue / forecastElectricityValue;
                if (result > 1.0) {
                    result = 1.0;
                }
                forecastPlant.setPercentage(result);
                String s = result >= 0.8 ? "优秀" : result >= 0.7 ? "良好" : result >= 0.6 ? "合格" : "不合格";
                forecastPlant.setGrade(s);
            } else {
                forecastPlant.setGrade("未知");
                forecastPlant.setPercentage(0.0);
            }
        });
        if (ForecastPlantDTO.SINGLE_DATA_TYPE.equals(query.getDataType())) {
            Map<String, Long> groupedCounts = new LinkedHashMap<>();
            for (String interval : predefinedIntervals) {
                int from = Integer.parseInt(interval.split("~")[0]);
                int to = Integer.parseInt(interval.split("~")[1]);
                groupedCounts.put(interval, records.stream()
                        .filter(plant -> plant.getPercentage() * 100 >= from && plant.getPercentage() * 100 <= to)
                        .count());
            }

            String grade = query.getGrade();
            if (StrUtil.isEmpty(grade)) {
                grade = "0~100";
            }
            int from = Integer.parseInt(grade.split("~")[0]);
            int to = Integer.parseInt(grade.split("~")[1]);

            records = records.stream()
                    .filter(plant -> plant.getPercentage() * 100 >= from && plant.getPercentage() * 100 <= to)
                    .collect(Collectors.toList());
            for (ForecastPlantVO record : records) {
                record.setPercentage(record.getPercentage() * 100);
            }
            if (query.getIsAsc() && "percentage".equals(query.getOrder())) {
                List<ForecastPlantVO> pageData = getGradePlantPageData(records, currentPage, pageSize, true);
                hashMap.put("pageData", pageData);
            } else {
                List<ForecastPlantVO> pageData = getGradePlantPageData(records, currentPage, pageSize, false);
                hashMap.put("pageData", pageData);
            }
            hashMap.put("groupedByPercentages", groupedCounts);
        } else if (ForecastPlantDTO.MULTI_DATA_TYPE.equals(query.getDataType())) {
            for (ForecastPlantVO record : records) {
                record.setPercentage(record.getPercentage() * 100);
            }
            Map<String, List<ForecastPlantVO>> groupedRecords = new LinkedHashMap<>();
            for (String interval : predefinedIntervals) {
                groupedRecords.put(interval, new ArrayList<>());
            }
            for (ForecastPlantVO vo : records) {
                double percentage = (Double.parseDouble(vo.getTodayElectricity()) / Double.parseDouble(vo.getForecastElectricity())) * 100;
                String intervalKey = getIntervalKey(percentage, predefinedIntervals);
                // 将 vo 添加到对应的区间列表中
                groupedRecords.get(intervalKey).add(vo);
            }
            hashMap.put("allGroupedRecords", groupedRecords);
        } else {
            throw new BusinessException(ResultEnum.REQUESTPARAM_ERROR);
        }
        return hashMap;
    }

    /**
     * 获取区间键
     *
     * @param percentage 百分比
     * @param predefinedIntervals 预定义区间列表
     * @return String 区间键
     */
    private static String getIntervalKey(double percentage, List<String> predefinedIntervals) {
        if (percentage > 100) {
            percentage = 100;
        }
        for (String interval : predefinedIntervals) {
            String[] bounds = interval.split("~");
            int lowerBound = Integer.parseInt(bounds[0]);
            int upperBound = Integer.parseInt(bounds[1]);

            if (percentage >= lowerBound && percentage <= upperBound) {
                return interval;
            }

        }
        return "Unknown Range";
    }

    /**
     * 获取当前预测电量
     *
     * 根据电站ID获取当前的发电预测电量
     *
     * @param plantId 电站唯一标识符
     * @return String 当前预测电量结果
     */
    @Override
    public String getCurrentForecastElectricity(String plantId) {
        return forecastMapper.getCurrentForecastElectricity(plantId);
    }

    /**
     * 获取评估图表数据
     *
     * 根据电站ID和日期获取电站发电评估图表数据
     *
     * @param plantId 电站唯一标识符
     * @param date 查询日期
     * @return HashMap<String, List<PlantPowerVO>> 评估图表数据映射
     */
    @Override
    public HashMap<String, List<PlantPowerVO>> getEvaluateChart(String plantId, String date) {
        HashMap<String, List<PlantPowerVO>> hashMap = new HashMap<>();
        // 预测功率
        List<PlantPowerVO> forecastPower = forecastMapper.getPredictPower(plantId, date);
        hashMap.put("forecastPower", forecastPower);
        // 实际功率
        List<PlantPowerVO> realPower = deviceServiceClient.getTotalPowerListByPlantUid(plantId, date);
        hashMap.put("realPower", realPower);
        return hashMap;
    }

    /**
     * 获取天气数据
     *
     * 根据电站ID获取该电站的天气数据信息
     *
     * @param plantId 电站唯一标识符
     * @return WeatherDataVO 天气数据视图对象
     */
    @Override
    public WeatherDataVO getWeatherData(String plantId) {

        // 根据工作效率排名获取默认 plantId
        if (StrUtil.isBlank(plantId)) {
            List<String> plantNameList = plantMapper.getPlantNameListInWeatherStation("0C");

            PageResult<String> pageList = weatherStationService.getPageList(new WeatherStationQuery());
            List<String> cities = pageList.getList();
            if (CollUtil.isEmpty(cities)) {
                throw new BusinessException("暂无数据");
            }
            PowerPlantInfoQueryDTO query = new PowerPlantInfoQueryDTO();
            query.setCity(cities);
            query.setCurrentPage(1);
            query.setPageSize(1);
            query.setPlantName(plantNameList);
            // 户租才有气象站
            query.setProjectSpecial(ProjectTypeEnum.HOUSEHOLD_LEASE.projectID.toString());
            IPage<WorkEfficiencyVO> page = plantService.getPlantElectricityRank(query);
            List<WorkEfficiencyVO> records = page.getRecords();
            if (CollUtil.isEmpty(records)) {
                throw new BusinessException("暂无数据");
            }
            plantId = records.get(0).getPlantUid();
        }

        Date date = new Date();
        String begin = DateUtils.getDayStart(date);
        String end = DateUtils.getDayEnd(date);
        WeatherDataVO weatherData = new WeatherDataVO();
        HashMap<String, Object> hashMap = verifyPlantId(plantId);
        PlantVO plantInfo = (PlantVO) hashMap.get("plantInfo");
        BigDecimal salePrice = plantInfo.getSalePrice();
        PlantAddressDTO plantAddressDTO = (PlantAddressDTO) hashMap.get("plantAddressDTO");
        List<MeteorologyEntity> meteorologyEntities = getMeteorologyListByPlantId(plantId, begin, end);
        List<WeatherEntity> weatherEntities = weatherMapper.selectList("bto_weather_" + DateUtils.getCurrentMonth(date), begin, end, plantAddressDTO.getCity());
        long currentTime = new Date().getTime();
        // 假设第一条数据为距离当前时间最近的一条天气数据
        WeatherEntity latestWeatherEntity = weatherEntities.get(0);
        long weatherEntityTimeDifference = Math.abs(latestWeatherEntity.getTime().getTime() - currentTime);
        for (WeatherEntity weatherEntity : weatherEntities) {
            Date entityTime = weatherEntity.getTime();
            // 天气预报时间加半小时，大于当前时间，则设为最新的天气预报信息
            // 如当前时间早上9点20分，天气预报为九点整，则取九点的天气预报
            // 如当前时间早上9点35分，天气预报为九点整，添加半小时不大于当前时间，则取十点整的天气预报
            if (entityTime.getTime() + 1800000 > currentTime) {
                break;
            }
            if (Math.abs(entityTime.getTime() - currentTime) < weatherEntityTimeDifference) {
                latestWeatherEntity = weatherEntity;
                weatherEntityTimeDifference = Math.abs(entityTime.getTime() - currentTime);
            }
        }
        // 获取最新一条气象信息
        MeteorologyEntity meteorologyEntity = meteorologyEntities.get(meteorologyEntities.size() - 1);
        ForecastBaseInfoVO convert = ForecastBaseInfoConvert.INSTANCE.convert(meteorologyEntity, latestWeatherEntity);
        weatherData.setForecastBaseInfoVO(convert);

        // 气象基础信息
        ForecastBaseInfoVO forecastPlantData = getPlantBaseInfo(plantInfo, weatherData.getForecastBaseInfoVO());
        forecastPlantData.setWindDirection(WindEnum.getValueByName(forecastPlantData.getWindDirection()));
        weatherData.setForecastBaseInfoVO(forecastPlantData);

        String predictElectricity = getCurrentForecastElectricity(plantId);
        if (StrUtil.isEmpty(predictElectricity)) {
            throw new BusinessException("电站模型尚未完善，发电预测失败");
        }
        BigDecimal futureTodayElectricity = new BigDecimal(predictElectricity);
        ForecastBaseInfoVO forecastBaseInfoVO = weatherData.getForecastBaseInfoVO();
        forecastBaseInfoVO.setForecastTodayElectricity(futureTodayElectricity.toString());

        forecastBaseInfoVO.setForecastTodayEarning(salePrice.multiply(futureTodayElectricity).toString());
        String todayElectricity = plantInfo.getTodayElectricity();
        String plantCapacity = plantInfo.getPlantCapacity();
        BigDecimal capacity = new BigDecimal(plantCapacity);
        forecastBaseInfoVO.setEquivalentHour(BusinessCalculateUtil.getRealEfficiencyPerHours(todayElectricity, plantCapacity));
        forecastBaseInfoVO.setPlantEfficiency(BusinessCalculateUtil.getWorkEfficiency(plantInfo.getPower().toString(), capacity.multiply(new BigDecimal("1000")).toString()));
        // 计算理论辐射量
        forecastBaseInfoVO.setTheoryRadiantQuantity(BusinessCalculateUtil.getRadiation(futureTodayElectricity.toString(), plantCapacity));
        forecastBaseInfoVO.setRadiantQuantity(BusinessCalculateUtil.getRadiation(todayElectricity, plantCapacity));

        return weatherData;
    }


    /**
     * 验证电站ID并获取电站信息和地址信息
     * 
     * 根据电站ID获取电站详细信息，并转换电站朝向枚举值，
     * 同时将电站信息转换为地址信息DTO用于后续处理。
     * 
     * @param plantId 电站唯一标识符
     * @return HashMap<String, Object> 包含电站信息和地址信息的映射
     * @throws BusinessException 当电站ID不存在时抛出异常
     */
    public HashMap<String, Object> verifyPlantId(String plantId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        PlantVO plantInfo = plantService.getPlantInfo(plantId);
        if (Objects.isNull(plantInfo)) {
            throw new BusinessException("电站ID填写错误或不存在");
        } else {
            String orientation = plantInfo.getOrientation();
            if (Objects.nonNull(orientation)) {
                if (OrientationEnum.divide.getName().equals(orientation)) {
                    plantInfo.setOrientation(OrientationEnum.HERRINGBONE.getName());
                }
                if (OrientationEnum.SAME.getName().equals(orientation)) {
                    plantInfo.setOrientation(OrientationEnum.IN_LINE.getName());
                }
            } else {
                plantInfo.setOrientation(OrientationEnum.NONE.getName());
            }
        }
        PlantAddressDTO plantAddressDTO = PlantAddressConvert.INSTANCE.convert(plantInfo);
        hashMap.put("plantAddressDTO", plantAddressDTO);
        hashMap.put("plantInfo", plantInfo);
        return hashMap;
    }

    /**
     * 获取等级评估电站分页数据
     *
     * 根据查询条件获取等级评估相关的电站列表信息，并进行分页处理。
     *
     * @param forecastPlants 电站列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param isAsc 是否升序
     * @return List<ForecastPlantVO> 分页后的电站列表
     */
    public List<ForecastPlantVO> getGradePlantPageData(List<ForecastPlantVO> forecastPlants, int currentPage, int pageSize, Boolean isAsc) {
        if (forecastPlants.isEmpty() || currentPage < 0) {
            return Collections.emptyList();
        }

        // 计算起始索引，注意列表索引从0开始，所以需要减1
        int startIndex = (currentPage - 1) * pageSize;

        // 判断起始索引是否超出总数据范围
        if (startIndex >= forecastPlants.size()) {
            // 如果超出范围，返回空列表
            return Collections.emptyList();
        }

        // 计算结束索引（包含）
        int endIndex = Math.min(startIndex + pageSize, forecastPlants.size());
        if (isAsc) {
            forecastPlants.sort(Comparator.comparing(ForecastPlantVO::getPercentage));
        } else {
            forecastPlants.sort(Comparator.comparing(ForecastPlantVO::getPercentage).reversed());

        }
        return new ArrayList<>(forecastPlants.subList(startIndex, endIndex));
    }


    /**
     * 获取匹配的辐射量
     *
     * 根据气象数据和辐射量数据获取匹配的辐射量数值
     *
     * @param weatherEntities 气象数据实体列表
     * @param weatherRadiations 辐射量数据视图对象列表
     * @return Double 匹配的辐射量数值
     */
    public Double acquiredRadiation(List<WeatherEntity> weatherEntities, List<WeatherRadiationVO> weatherRadiations) {
        List<WeatherRadiationVO> matchingEntities = new ArrayList<>();
        for (WeatherRadiationVO vo : weatherRadiations) {
            for (WeatherEntity weatherEntity : weatherEntities) {
                String format = DateUtil.format(weatherEntity.getTime(), "HH:mm:ss");
                if (vo.getTime().equals(format) && vo.getWeather().equals(weatherEntity.getWeather())) {
                    matchingEntities.add(vo);
                }
            }
        }
        return matchingEntities.stream().mapToDouble(WeatherRadiationVO::getAvgRadiation).sum();
    }

    /**
     * 获取电站基础信息
     *
     * 根据电站信息和预测基础信息获取电站的完整基础信息
     *
     * @param plantInfo 电站视图对象，包含电站基本信息
     * @param forecastBaseInfoVO 预测基础信息视图对象
     * @return ForecastBaseInfoVO 电站预测基础信息
     */
    @Override
    public ForecastBaseInfoVO getPlantBaseInfo(PlantVO plantInfo, ForecastBaseInfoVO forecastBaseInfoVO) {
        forecastBaseInfoVO.setTodayElectricity(plantInfo.getTodayElectricity());
        forecastBaseInfoVO.setPlantCapacity(plantInfo.getPlantCapacity());
        forecastBaseInfoVO.setPlantStatus(plantInfo.getPlantStatus());
        forecastBaseInfoVO.setTodayEarning(plantInfo.getTodayEarning());
        forecastBaseInfoVO.setPower(plantInfo.getPower());
        forecastBaseInfoVO.setLatitude(plantInfo.getLatitude());
        forecastBaseInfoVO.setLongitude(plantInfo.getLongitude());
        PlantInfoEntity plantInfoEntity = plantInfoService.getById(plantInfo.getPlantUid());
        forecastBaseInfoVO.setPlantReplenishInfo(PlantInfoConvert.INSTANCE.convert(plantInfoEntity));
        forecastBaseInfoVO.setOrientation(plantInfo.getOrientation());
        return forecastBaseInfoVO;
    }

    /**
     * 项目电量预测
     *
     * 根据项目ID和预测天数进行电量预测
     *
     * @param projectId 项目唯一标识符
     * @param size 预测天数
     * @return AbstractMap<String, String> 预测结果映射
     */
    @Override
    public AbstractMap<String, String> predictionElectByProject(String projectId, int size) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String today = DateUtil.today();
        // 构造Redis中存储未来15天预测发电量的key
        String redisKey = PREDICTION_ELECTRICITY + userInfo.getUserUid() + projectId + REDIS_SPLIT + today + REDIS_SPLIT + size;

        // 尝试从Redis中获取预测发电量
        Object obj = redisUtil.get(redisKey);
        // 如果存在，直接返回
        if (obj != null) {
            return (AbstractMap<String, String>) obj;
        }

        PlantEvaluationQuery query = new PlantEvaluationQuery();
        query.setProjectId(projectId);

        List<String> plantCities = getCity(userInfo, query);

        if (CollUtil.isEmpty(plantCities)) {
            return new HashMap<>();
        }
        List<DateRadiationVO> futureRadiationList = getFutureRadiationList(plantCities, size);
        TreeMap<String, String> map = new TreeMap<>();
        for (DateRadiationVO item : futureRadiationList) {
            String predictionElectricity = radiantQuantityService.getPredictionElectricityByRadiation(item.getRadiation(), projectId);
            map.put(item.getDate(), BusinessCalculateUtil.getRealElectricity(predictionElectricity));
        }

        redisUtil.set(redisKey, map, DateUtils.getExpTimeByEndOfToday());
        return map;
    }

    /**
     * 获取未来辐射量列表
     *
     * 根据城市列表和预测天数获取未来辐射量数据
     *
     * @param plantCities 城市列表
     * @param size 预测天数
     * @return List<DateRadiationVO> 未来辐射量视图对象列表
     */
    @NotNull
    private List<DateRadiationVO> getFutureRadiationList(List<String> plantCities, int size) {
        // 获取未来15天天气情况
        List<WeatherFutureVO> futureWeatherList = getFutureSevenDayWeatherList(plantCities, size);
        List<DateRadiationVO> futureRadiationList = new ArrayList<>();
        for (WeatherFutureVO weatherFutureVO : futureWeatherList) {
            // 获取天气字段
            String weather = weatherFutureVO.getWeather();
            Double average = weatherRadiationService.getRadiationByWeather(weather);
            if (average == null) {
                average = 0.0;
            }
            DateRadiationVO dateRadiationVO = new DateRadiationVO(new SimpleDateFormat(DateUtils.DATE_PATTERN).format(weatherFutureVO.getCollectDate()), Optional.ofNullable(average).orElse(0.0).toString());
            futureRadiationList.add(dateRadiationVO);
        }
        return futureRadiationList;
    }

    /**
     * 电量预测分析
     *
     * 根据项目ID进行电量预测分析
     *
     * @param projectId 项目唯一标识符
     * @return Map<String, Integer> 分析结果映射
     */
    @Override
    public Map<String, Integer> predictionElectAnalysis(String projectId) {
        RequireParamsDTO userInfo = globalParamUtil.getUserInfo();
        String today = DateUtil.today();
        String redisKey = ELECTRICITY_BENEFIT + userInfo.getUserUid() + projectId + REDIS_SPLIT + today;

        Object obj = redisUtil.get(redisKey);
        if (obj != null) {
            return (Map<String, Integer>) obj;
        }


        PlantEvaluationQuery query = new PlantEvaluationQuery();
        query.setProjectId(projectId);

        // 获取项目下存在气象站的city
        List<String> plantCities = getCity(userInfo, query);

        if (CollUtil.isEmpty(plantCities)) {
            return Collections.emptyMap();
        }

        List<PlantVO> plantIdWithCity = plantMapper.getPlantIdWithCity(userInfo, projectId);
        HashMap<String, BigDecimal> cityWithRadiationMap = new HashMap<>();


        for (String city : plantCities) {
            BigDecimal radiation = cityWithRadiationMap.getOrDefault(city, BigDecimal.valueOf(0));

            List<DateRadiationVO> futureRadiationList = getFutureRadiationList(Collections.singletonList(city), 15);
            for (DateRadiationVO dateRadiationVO : futureRadiationList) {
                BigDecimal bigDecimal = new BigDecimal(dateRadiationVO.getRadiation());
                radiation = radiation.add(bigDecimal);
            }
            cityWithRadiationMap.put(city, radiation);
        }

        ArrayList<BigDecimal> benefitList = new ArrayList<>();
        for (PlantVO plantVO : plantIdWithCity) {
            BigDecimal cityRadiation = cityWithRadiationMap.getOrDefault(plantVO.getCity(),BigDecimal.ZERO);
            //获取未来发电量
            String futureForecastData = forecastMapper.getFutureForecastData(plantVO.getPlantUid(), cityRadiation.doubleValue());

            BigDecimal predictElectricity = new BigDecimal(BusinessCalculateUtil.getRealElectricity(Optional.ofNullable(futureForecastData).orElse("0")));
            benefitList.add(predictElectricity.multiply(ELECTRICITY_BENEFIT_MULTIPLIER).setScale(2, RoundingMode.HALF_UP));
        }

        if (CollUtil.isEmpty(benefitList)) {
            return Collections.emptyMap();
        }
        Collections.sort(benefitList, Comparator.naturalOrder());
        Map<String, BigDecimal[]> complianceRanges = RangeUtils.generateComplianceRanges(5, benefitList.get(benefitList.size() - 1).toString());

        Map<String, Integer> countMap = RangeUtils.getCountMap(benefitList, null, complianceRanges);

        redisUtil.set(redisKey, countMap, DateUtils.getExpTimeByEndOfToday());
        return countMap;
    }


    // 获取项目下存在气象站的city
    @NotNull
    public List<String> getCity(RequireParamsDTO userInfo, PlantEvaluationQuery query) {
        // 获取存在气象站的city
        WeatherStationQuery weatherStationQuery = new WeatherStationQuery();
        weatherStationQuery.setCurrentPage(-1);
        weatherStationQuery.setPageSize(-1);
        PageResult<String> pageList = weatherStationService.getPageList(weatherStationQuery);
        List<String> weatherStationCityList = pageList.getList();

        // 获取项目下的所有city
        List<String> plantCities = plantMapper.getPlantCities(userInfo, query);

        // 交集
        plantCities.retainAll(weatherStationCityList);
        return plantCities;
    }

    /**
     * 电量预测
     *
     * 根据项目ID进行电量预测计算
     *
     * @param projectId 项目唯一标识符
     * @return HashMap<String, BigDecimal> 预测电量结果映射
     */
    @Override
    public HashMap<String, BigDecimal> predictionElectricity(String projectId) {

        HashMap<String, BigDecimal> map = new HashMap<>();
        AbstractMap<String, String> oneDayMap = this.predictionElectByProject(projectId, 1);

        AbstractMap<String, String> fifteenDayMap = this.predictionElectByProject(projectId, 15);
        AbstractMap<String, String> thirtyDayMap = this.predictionElectByProject(projectId, 30);

        BigDecimal one = getBenefit(oneDayMap);
        map.put("one", one);

        BigDecimal fifteen = getBenefit(fifteenDayMap);
        map.put("fifteen", fifteen);

        BigDecimal thirty = getBenefit(thirtyDayMap);
        map.put("thirty", thirty);
        return map;
    }

    /**
     * 计算收益
     *
     * 根据预测电量数据计算收益
     *
     * @param oneDayMap 预测电量数据映射
     * @return BigDecimal 收益结果
     */
    @NotNull
    private static BigDecimal getBenefit(AbstractMap<String, String> oneDayMap) {
        BigDecimal result = BigDecimal.ZERO;
        for (Map.Entry<String, String> entry : oneDayMap.entrySet()) {
            String electricity = entry.getValue();
            if (electricity != null) {
                BigDecimal bigDecimal = new BigDecimal(electricity);
                result = result.add(bigDecimal);
            }
        }
        // 设置结果保留两位小数
        return result.multiply(ELECTRICITY_BENEFIT_MULTIPLIER).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}
