package com.bto.plant.service;

import com.bto.commons.pojo.dto.PlantEvaluationQuery;
import com.bto.commons.pojo.vo.CityMapVO;
import com.bto.commons.pojo.vo.PlantEvaluationStaticsVO;
import com.bto.commons.pojo.vo.ProjectInfoVO;
import org.apache.ibatis.annotations.MapKey;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 电站评估服务接口
 * 
 * 定义电站评估相关的业务接口，提供电站发电量预测、实际发电量对比、
 * 辐射量分析等评估功能。
 * 
 * <AUTHOR>
 * @since 2024/1/5 9:42
 */
public interface PlantEvaluationService {
    /**
     * 获取电站评估统计信息
     * 
     * 根据查询条件获取电站评估的统计信息，包括电站总数、总装机容量等关键指标
     * 
     * @param query 查询条件
     * @return 电站评估统计信息
     */    
    PlantEvaluationStaticsVO statics(PlantEvaluationQuery query);

    /**
     * 获取辐射量数据（按日期）
     * 
     * 根据查询条件获取指定时间范围内的辐射量数据，按日期维度进行统计
     * 
     * @param query 查询条件
     * @return 辐射量数据列表
     */
    List<Map<String, String>> getRadiationWithDate(PlantEvaluationQuery query);

    /**
     * 获取基于辐射量的月度预测发电量
     * 
     * 根据辐射量数据计算月度预测发电量
     * 
     * @param query 查询条件
     * @return 月度预测发电量数据
     */
    @MapKey("collectDate")
    Map<String, String> getPreElectricityByRadiationWithMonth(PlantEvaluationQuery query);

    /**
     * 获取实际发电量
     * 
     * 根据查询条件获取实际发电量数据，用于发电量对比分析
     * 
     * @param query 查询条件
     * @return 实际发电量数据
     */
    BigDecimal getRealElectricity(PlantEvaluationQuery query);

    /**
     * 获取实际发电量数据（按日期）
     * 
     * 根据查询条件获取指定时间范围内的实际发电量数据，按日期维度进行统计
     * 
     * @param query 查询条件
     * @return 实际发电量数据列表
     */
    List<Map<String, String>> getElectricityWithDate(PlantEvaluationQuery query);

    /**
     * 获取三个月发电量对比数据
     * 
     * 获取最近三个月的发电量数据进行对比分析
     * 
     * @param query 查询条件
     * @return 三个月发电量对比数据
     */
    Map<String, String> getElectricityThreeMonthsCompare(PlantEvaluationQuery query);

    /**
     * 获取电站评估信息
     * 
     * 获取电站评估的综合信息，包括实际效率和预测效率等关键指标
     * 
     * @param query 查询条件
     * @return 电站评估信息
     */
    Map<String, String> getPlantEvaluationInfo(PlantEvaluationQuery query);

    /**
     * 获取区域电站树形结构
     * 
     * 根据项目ID获取区域电站的树形结构数据，支持地图展示
     * 
     * @param projectId 项目ID
     * @return 区域电站树形结构列表
     */
    List<CityMapVO> getCityAreaTreeWithMapId(String projectId);

    /**
     * 获取气象站项目树形结构
     * 
     * 获取用于气象站展示的项目树形结构数据
     * 
     * @return 项目树形结构列表
     */
    List<ProjectInfoVO> getProjectTreeForWeatherStation();
}
