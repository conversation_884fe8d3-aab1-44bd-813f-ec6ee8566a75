package com.bto.plant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.commons.pojo.dto.WeatherStationQuery;
import com.bto.commons.pojo.entity.WeatherEntity;
import com.bto.commons.pojo.entity.WeatherStationEntity;
import com.bto.commons.pojo.vo.PageResult;
import com.bto.commons.pojo.vo.StationLatestVO;
import com.bto.commons.pojo.vo.WeatherStationVO;

import java.util.List;

/**
 * 气象站服务接口
 * 
 * 提供气象站相关的业务服务，包括气象站管理、城市天气数据获取、监测数据查询等功能
 *
 * <AUTHOR>
 * @since 2024/1/6 9:40
 */
public interface WeatherStationService extends IService<WeatherStationEntity> {

    /**
     * 根据城市列表获取气象站UID列表
     * 
     * 根据城市名称列表查询对应的气象站唯一标识符列表
     *
     * @param cities 城市名称列表
     * @return List<String> 气象站UID列表
     */
    List<String> getStationUidListByCities(List<String> cities);

    /**
     * 分页获取气象站列表
     * 
     * 根据查询条件分页获取气象站信息
     *
     * @param query 气象站查询条件对象
     * @return PageResult<String> 分页结果对象，包含气象站信息
     */
    PageResult<String> getPageList(WeatherStationQuery query);

    /**
     * 根据城市获取气象站列表
     * 
     * 根据城市名称查询该城市下的所有气象站信息
     *
     * @param city 城市名称
     * @return List<WeatherStationVO> 气象站视图对象列表
     */
    List<WeatherStationVO> getWeatherStationByCity(String city);

    /**
     * 获取所有城市列表
     * 
     * 查询系统中配置的所有城市名称列表
     *
     * @return List<String> 城市名称列表
     */
    List<String> getAllCity();

    /**
     * 根据城市获取气象站监测数据
     * 
     * 根据城市列表和指定日期获取各城市气象站的最新监测数据
     *
     * @param allCity 城市名称列表
     * @param date 查询日期，格式为yyyy-MM-dd
     * @return List<StationLatestVO> 气象站最新监测数据列表
     */
    List<StationLatestVO> getStationLatestDataByCity(List<String> allCity,String date);

    /**
     * 根据城市获取区域列表
     * 
     * 根据城市名称列表查询对应的区域信息
     *
     * @param cities 城市名称列表
     * @return List<String> 区域信息列表
     */
    List<String> getAreaByCities(List<String> cities);

    /**
     * 创建气象站
     * 
     * 为指定电站创建气象站记录
     *
     * @param plantName 电站名称
     * @param operatorId 操作员ID
     */
    void create(String plantName, String operatorId);

    /**
     * 分页查询气象站
     * 
     * 根据查询条件分页查询气象站实体信息
     *
     * @param query 气象站查询条件对象
     * @return PageResult<WeatherStationEntity> 分页结果对象，包含气象站实体信息
     */
    PageResult<WeatherStationEntity> page(WeatherStationQuery query);

    /**
     * 变更操作员
     * 
     * 变更气象站的操作员信息
     *
     * @param stationUid 气象站唯一标识符
     * @param operatorId 新操作员ID
     * @param oldOperatorId 原操作员ID
     */
    void changeOperator(String stationUid, String operatorId, String oldOperatorId);

    /**
     * 根据城市获取最新天气信息
     * 
     * 根据城市名称获取该城市最新的天气信息
     *
     * @param city 城市名称
     * @return WeatherEntity 天气信息实体对象
     */
    WeatherEntity getWeatherByPlantUid(String city);
}