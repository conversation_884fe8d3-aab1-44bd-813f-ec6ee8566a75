package com.bto.plant.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.commons.converter.vo.BtoPlantRemarksConvert;
import com.bto.commons.pojo.dto.BtoPlantRemarksDTO;
import com.bto.commons.pojo.entity.BtoPlantRemarksEntity;
import com.bto.commons.pojo.vo.BtoPlantRemarksVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.BtoPlantRemarksDao;
import com.bto.plant.service.BtoPlantRemarksService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 电站备注信息服务实现类
 * 
 * 电站备注信息相关业务服务的具体实现类，提供电站备注的增删改查、分页查询等功能。
 *
 * <AUTHOR>
 * @since 1.0.0 2024-06-20
 */
@Service
@AllArgsConstructor
public class BtoPlantRemarksServiceImpl extends BaseServiceImpl<BtoPlantRemarksDao, BtoPlantRemarksEntity> implements BtoPlantRemarksService {

    /**
     * 分页查询电站备注信息
     * 
     * 根据电站ID和分页参数查询电站备注信息，支持按创建时间降序排列，
     * 确保最新添加的备注信息优先展示。
     * 
     * @param query 查询条件（包含电站ID、分页参数）
     * @return 电站备注信息分页数据，按创建时间降序排列
     */ 
    @Override
    public IPage<BtoPlantRemarksEntity> page(BtoPlantRemarksDTO query) {
        IPage<BtoPlantRemarksEntity> page = new Page<>(query.getCurrentPage(), query.getPageSize());
        LambdaQueryWrapper<BtoPlantRemarksEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(BtoPlantRemarksEntity::getPlantUid, query.getPlantUid());
        wrapper.orderByDesc(true, BtoPlantRemarksEntity::getCreateTime);
        IPage<BtoPlantRemarksEntity> btoPlantRemarksEntityIPage = baseMapper.selectPage(page, wrapper);
        return page;
    }

    /**
     * 保存电站备注信息
     * 
     * 将电站备注信息视图对象转换为实体对象并保存到数据库中，用于新增电站备注记录。
     * 该方法会保留所有原始信息，包括创建时间和更新时间。
     * 
     * @param vo 电站备注信息视图对象，包含完整的备注信息
     */ 
    @Override
    public void save(BtoPlantRemarksVO vo) {
        BtoPlantRemarksEntity entity = BtoPlantRemarksConvert.INSTANCE.convert(vo);
        baseMapper.insert(entity);
    }

    /**
     * 更新电站备注信息
     * 
     * 将电站备注信息视图对象转换为实体对象并更新到数据库中，用于修改现有电站备注记录。
     * 该方法会根据实体ID进行更新操作，保留原有创建时间，仅更新内容和更新时间。
     * 
     * @param vo 电站备注信息视图对象，包含需要更新的备注信息
     */ 
    @Override
    public void update(BtoPlantRemarksVO vo) {
        BtoPlantRemarksEntity entity = BtoPlantRemarksConvert.INSTANCE.convert(vo);
        updateById(entity);
    }

    /**
     * 清空电站备注信息
     * 
     * 根据电站唯一标识清空该电站的所有备注信息，该操作不可撤销。
     * 用于电站重新评估或数据重置场景，会删除该电站下的所有备注记录。
     * 
     * @param plantUid 电站唯一标识，用于指定需要清空备注的目标电站
     */ 
    @Override
    public void clear(String plantUid) {
        baseMapper.clear(plantUid);
    }
}