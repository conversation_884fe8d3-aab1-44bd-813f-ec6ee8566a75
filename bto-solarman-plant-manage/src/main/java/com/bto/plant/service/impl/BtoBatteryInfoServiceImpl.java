package com.bto.plant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bto.commons.pojo.entity.BtoBatteryInfo;
import com.bto.commons.pojo.vo.BatteryCellPackVO;
import com.bto.commons.pojo.vo.BatteryInfoVO;
import com.bto.commons.service.impl.BaseServiceImpl;
import com.bto.plant.dao.BtoBatteryInfoDao;
import com.bto.plant.service.BtoBatteryInfoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 电池信息管理服务实现类
 * 
 * 电池信息相关业务服务的具体实现类，提供电池簇信息的查询和管理功能。
 * 支持根据逆变器序列号获取电池簇的详细信息，包括电芯组数据等。
 *
 * <AUTHOR>
 * @since 2025/4/24
 */
@Service
@AllArgsConstructor
public class BtoBatteryInfoServiceImpl extends BaseServiceImpl<BtoBatteryInfoDao, BtoBatteryInfo> implements BtoBatteryInfoService {

    private final BtoBatteryInfoDao dao;

    /**
     * 获取电池簇信息
     * 
     * 根据逆变器序列号获取电池簇的详细信息，包括电芯组数据和电池基本信息。
     * 该方法通过逆变器序列号精确查询对应的电池信息，并将数据库中的JSON格式电芯组数据
     * 解析为标准的电芯组视图对象，最终返回包含完整电池信息的视图对象。
     * 
     * @param inverterSn 逆变器序列号，用于精确定位对应的电池簇信息
     * @return 电池信息视图对象，包含电池基本信息和电芯组数据
     */
    @Override
    public BatteryInfoVO getBatteryClusterInfo(String inverterSn) {
        LambdaQueryWrapper<BtoBatteryInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BtoBatteryInfo::getInverterSn, inverterSn);
        BtoBatteryInfo btoBatteryInfo = baseMapper.selectOne(queryWrapper);
        List<BatteryCellPackVO> batteryCellPackVOS = JSON.parseArray(btoBatteryInfo.getBatteriesList(), BatteryCellPackVO.class);
        BatteryInfoVO batteryInfoVO = new BatteryInfoVO();
        batteryInfoVO.setBatteryCellPacks(batteryCellPackVOS);
        BeanUtil.copyProperties(btoBatteryInfo, batteryInfoVO);
        return batteryInfoVO;
    }
}