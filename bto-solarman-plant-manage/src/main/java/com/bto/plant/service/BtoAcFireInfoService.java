package com.bto.plant.service;

import com.bto.commons.pojo.dto.BatteryQuery;
import com.bto.commons.pojo.entity.BtoAcFireInfoEntity;
import com.bto.commons.pojo.vo.BtoAcFireInfoVO;
import com.bto.commons.service.BaseService;

import java.util.List;

/**
 * 空调消防信息服务接口
 * 
 * 提供空调消防信息的实时数据查询和处理服务，包括电池保护信息等相关功能
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-04-22
 */
public interface BtoAcFireInfoService extends BaseService<BtoAcFireInfoEntity> {

    /**
     * 获取电池保护信息
     * 
     * 根据电池查询条件获取电池保护相关的空调消防信息
     *
     * @param query 电池查询条件对象，包含查询参数和过滤条件
     * @return List<BtoAcFireInfoVO> 空调消防信息视图对象列表
     */
    List<BtoAcFireInfoVO> getBatteryGuardInfo(BatteryQuery query);
}