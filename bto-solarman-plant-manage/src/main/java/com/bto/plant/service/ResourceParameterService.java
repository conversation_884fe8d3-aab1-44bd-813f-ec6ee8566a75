package com.bto.plant.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.commons.pojo.entity.ResourcesParameter;

/**
 * 资源参数服务接口
 * 
 * 提供资源参数相关业务接口，继承MyBatis Plus的IService接口获取通用CRUD功能
 *
 * <AUTHOR>
 * @since 2024/1/7 14:47
 */
public interface ResourceParameterService extends IService<ResourcesParameter> {
    /**
     * 根据城市获取资源参数信息
     * 
     * 根据城市名称查询对应的资源参数配置信息
     * 
     * @param city 城市名称
     * @return 资源参数实体对象
     */
    ResourcesParameter getInfoByCity(String city);
}
