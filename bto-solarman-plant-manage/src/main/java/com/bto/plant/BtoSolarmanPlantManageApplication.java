package com.bto.plant;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 光伏管理平台启动类
 */
@EnableFeignClients(basePackages = {"com.bto.api.feign.*"})
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.bto.*"})
@MapperScan(basePackages = {"com.bto.*.dao"})
@Configuration
@EnableAsync
@EnableCaching
public class BtoSolarmanPlantManageApplication {
    /**
     * 应用启动入口
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(BtoSolarmanPlantManageApplication.class);
        springApplication.run(args);
    }

}
