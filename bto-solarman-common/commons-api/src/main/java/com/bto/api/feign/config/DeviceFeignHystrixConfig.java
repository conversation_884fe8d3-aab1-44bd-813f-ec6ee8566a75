package com.bto.api.feign.config;

import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import feign.Request;
import feign.Target;
import feign.hystrix.SetterFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Method;

/**
 * DeviceServiceClient 专用的 Feign 配置：仅上调 getIotCardByPage 的超时时间。
 * 同时配置 Hystrix 超时和 Feign HTTP 读取超时，不影响其他 Feign Client 或方法。
 */
@Configuration
public class DeviceFeignHystrixConfig {

    @Bean
    public SetterFactory deviceSetterFactory() {
        return new SetterFactory() {
            @Override
            public HystrixCommand.Setter create(Target<?> target, Method method) {
                String groupKey = target.name();
                String commandKey = target.name() + ":" + method.getName();

                HystrixCommandProperties.Setter props = HystrixCommandProperties.Setter();
                // 默认使用 Hystrix 的默认超时
                // 针对 getIotCardByPage 单独放宽超时时间，避免大分页查询导致的误判超时
                if ("getIotCardByPage".equals(method.getName())) {
                    props = props.withExecutionTimeoutInMilliseconds(60000); // 60秒
                }

                return HystrixCommand.Setter
                        .withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupKey))
                        .andCommandKey(HystrixCommandKey.Factory.asKey(commandKey))
                        .andCommandPropertiesDefaults(props);
            }
        };
    }

    /**
     * 配置 Feign 的 HTTP 超时时间，解决 "Read timed out" 问题
     */
    @Bean
    public Request.Options feignRequestOptions() {
        // connectTimeout: 连接超时 10秒
        // readTimeout: 读取超时 50秒 (应小于 Hystrix 超时)
        return new Request.Options(10000, 50000);
    }
}

