package com.bto.api.feign.config;

import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import feign.Target;
import feign.hystrix.SetterFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Method;

/**
 * DeviceServiceClient 专用的 Hystrix 配置：仅上调 getIotCardByPage 的超时时间。
 * 不影响其他 Feign Client 或方法。
 */
@Configuration
public class DeviceFeignHystrixConfig {

    @Bean
    public SetterFactory deviceSetterFactory() {
        return new SetterFactory() {
            @Override
            public HystrixCommand.Setter create(Target<?> target, Method method) {
                String groupKey = target.name();
                String commandKey = target.name() + ":" + method.getName();

                HystrixCommandProperties.Setter props = HystrixCommandProperties.Setter();
                // 默认使用 Hystrix 的默认超时
                // 针对 getIotCardByPage 单独放宽超时时间，避免大分页查询导致的误判超时
                if ("getIotCardByPage".equals(method.getName())) {
                    props = props.withExecutionTimeoutInMilliseconds(30000);
                }

                return HystrixCommand.Setter
                        .withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupKey))
                        .andCommandKey(HystrixCommandKey.Factory.asKey(commandKey))
                        .andCommandPropertiesDefaults(props);
            }
        };
    }
}

